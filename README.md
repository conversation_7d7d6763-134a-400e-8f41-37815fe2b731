# TruckGPT Deployment Guide

This guide covers the deployment of TruckGPT, an application utilizing FastAPI, Celery and Redis, orchestrated with Docker Compose. TruckGPT leverages OpenAI's GPT-4 and GPT-4-Vision models for processing and generating responses. This setup is ideal for developing scalable, efficient applications that require real-time data processing and AI capabilities.

## Prerequisites

Before proceeding, ensure you have the following installed on your system:

- Docker
- Docker Compose

Installation guides for Docker and Docker Compose can be found on the official Docker website.

## Overview

The `docker-compose.yml` file defines the services required to run the TruckGPT application:

- **fastapi**: The web application built with FastAPI. It serves as the primary interface for processing requests and returning responses.
- **celery**: The Celery worker for handling asynchronous tasks.
- **redis**: <PERSON><PERSON> serves as the message broker for Celery and can also be used for caching.

## Configuration

Before starting the services, you may need to customize the environment variables in the `docker-compose.yml` file, especially the OpenAI API keys, to match your setup.

## Deployment Steps

1. **Clone the Repository**: Ensure you have the project repository cloned to your local machine.
2. **Navigate to the Project Directory**: Change into the directory containing the Docker Compose file.
    ```
    cd path/to/project
    ```
3. **Build and Start the Services**: Use Docker Compose to build and start the services defined in the `docker-compose.yml` file.
    ```
    docker-compose up --build
    ```
   This command builds the images if they don't exist and starts the services. Use `-d` to run in detached mode.

4. **Verify the Services**: Once the services are up, you can access the FastAPI application at `http://localhost:8000`. The Redis services will also be accessible on their respective ports (`6379` for Redis).

## Environment Variables

- `SENTRY_DSN`: The Sentry DSN for error tracking.
- `REDIS_URI`: The URI for the Redis server.
- `OPENAI_URL`: The URL for the OpenAI API (GPT-4).
- `OPENAI_MODEL`: Specifies the OpenAI model to use.
- `OPENAI_API_KEY`: The API key for accessing OpenAI's GPT-4.
- `OPENAI_API_VERSION`: The version of the OpenAI API.
- `OPENAI_VISION_URL`: The URL for the OpenAI Vision API.
- `OPENAI_VISION_MODEL`: Specifies the OpenAI vision model to use.
- `OPENAI_VISION_API_KEY`: The API key for accessing OpenAI's GPT-4-Vision.

## Scaling

To scale the application, adjust the number of worker instances for the `fastapi` and `celery` services as needed, keeping in mind the resources available on your Docker host.

## Maintenance

- **Updating the Application**: To update the application, pull the latest changes from your repository and rebuild the Docker images.
- **Backup and Restore**: Regularly backup your Redis data. You can adjust the volume mounts in the `docker-compose.yml` file to point to your backup directories.

## Troubleshooting

- If you encounter any issues during deployment, check the logs of the individual services using `docker-compose logs [service_name]`.
- Ensure all environment variables are correctly set, especially those related to external services like OpenAI.

For detailed information on FastAPI, Celery and Redis, refer to their respective documentation. This guide aims to provide a quick overview of deploying TruckGPT with Docker Compose.