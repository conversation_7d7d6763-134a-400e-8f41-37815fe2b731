volumes:
  redis_data:

services:
  fastapi:
    container_name: truckgpt_fastapi
    build:
      context: .
      dockerfile: Dockerfile
    command: bash -c 'gunicorn -k uvicorn.workers.UvicornWorker -w 6 -t 120 --worker-connections 1000 --bind 0.0.0.0:8000 app.main:app'
    restart: always
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    depends_on:
      - redis
    environment:
      - SENTRY_DSN=https://<EMAIL>/4504967126581248
      - REDIS_URI=redis://redis:6379/0
      - OPENAI_URL=https://app-cg5x4m3vnj6t2.redisland-af77da42.westus3.azurecontainerapps.io/
      - OPENAI_MODEL=gpt-4-1106
      - OPENAI_API_KEY=********************************
      - OPENAI_API_VERSION=2024-04-01-preview
      - OPENAI_VISION_URL=***********************************
      - OPENAI_VISION_MODEL=gpt-4o
      - OPENAI_VISION_API_KEY=********************************
      - OPENAI_VISION_API_VERSION=2024-08-01-preview
      - OPENAI_CATEGORY_URL=https://ai-stage04.openai.azure.com/
      - OPENAI_CATEGORY_MODEL=gpt-35-turbo-1106
      - OPENAI_CATEGORY_API_KEY=********************************
      - OPENAI_CATEGORY_API_VERSION=2024-04-01-preview
      - DATABASE_URL=postgresql://azuredbuser:<EMAIL>:5432/truckgpt
  
  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    ports:
      - "5672:5672"   # AMQP
      - "15672:15672" # Web UI
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
  
  celery:
    container_name: truckgpt_celery
    build:
      context: .
      dockerfile: Dockerfile
    command: celery -A app.worker worker -Q truckgpt --loglevel=info
    restart: always
    volumes:
      - .:/app
    depends_on:
      - redis
      - rabbitmq
    environment:
      - SENTRY_DSN=https://<EMAIL>/4504967126581248
      - CELERY_BROKER_URI=amqp://guest:guest@rabbitmq:5672//
      - REDIS_URI=redis://redis:6379/0
      - OPENAI_URL=https://app-cg5x4m3vnj6t2.redisland-af77da42.westus3.azurecontainerapps.io/
      - OPENAI_MODEL=gpt-4-1106
      - OPENAI_API_KEY=********************************
      - OPENAI_API_VERSION=2024-04-01-preview
      - OPENAI_VISION_URL=***********************************
      - OPENAI_VISION_MODEL=gpt-4o
      - OPENAI_VISION_API_KEY=********************************
      - OPENAI_VISION_API_VERSION=2024-08-01-preview
      - OPENAI_CATEGORY_URL=https://ai-stage04.openai.azure.com/
      - OPENAI_CATEGORY_MODEL=gpt-35-turbo-1106
      - OPENAI_CATEGORY_API_KEY=********************************
      - OPENAI_CATEGORY_API_VERSION=2024-04-01-preview
      - DATABASE_URL=postgresql://azuredbuser:<EMAIL>:5432/truckgpt
  redis:
    container_name: truckgpt_redis
    image: redis:7.2
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data