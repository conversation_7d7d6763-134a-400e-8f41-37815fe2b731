name: Staging Auto Deployment

# When this action will be executed
on:
  # Automatically trigger it when detected changes in repo
  push:
    branches: [main]

  # Allow manual trigger
  workflow_dispatch:

env:
  TRUCKGPT_IMAGE_NAME: 629070900093.dkr.ecr.us-east-1.amazonaws.com/truckgpt-service
  TRUCKGPT_IMAGE_TAG: ${{ github.sha }}
  PROJECT_DIR: /home/<USER>/deploy

jobs:
  build:
    name: Build and Push
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and Push
        run: |
          docker build . -t ${{ env.TRUCKGPT_IMAGE_NAME }}:${{ env.TRUCKGPT_IMAGE_TAG }}
          docker push ${{ env.TRUCKGPT_IMAGE_NAME }}:${{ env.TRUCKGPT_IMAGE_TAG }}

  deploy:
    name: Deploy to AWS EC2
    needs: build
    runs-on: [ dev ]
    defaults:
      run:
        working-directory: ${{ env.PROJECT_DIR }}
    environment:
      name: staging
      url: https://app.datatruck.org
    steps:
      - name: Pull
        run: |
          echo "Pull the latest image"
          docker pull ${{ env.TRUCKGPT_IMAGE_NAME }}:${{ env.TRUCKGPT_IMAGE_TAG }}

      - name: Restart
        run: |
          echo "Restarting services"
          ./run.sh start truckgpt

      - name: Show logs
        run: |
          cd truckgpt && docker compose logs truckgpt truckgpt-celery --timestamps --tail 1000
          echo "Deployment completed"
