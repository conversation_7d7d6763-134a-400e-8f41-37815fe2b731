# Dockerfile

# pull the official docker image
FROM python:3.11

# Installing necessary packages
RUN apt update && apt install -y vim poppler-utils poppler-data curl libgl1-mesa-glx libglib2.0-0

# Downloading gcloud package
RUN curl https://dl.google.com/dl/cloudsdk/release/google-cloud-sdk.tar.gz > /tmp/google-cloud-sdk.tar.gz

# Installing the package
RUN mkdir -p /usr/local/gcloud \
  && tar -C /usr/local/gcloud -xvf /tmp/google-cloud-sdk.tar.gz

# Disable usage reporting for gcloud
RUN /usr/local/gcloud/google-cloud-sdk/bin/gcloud config set disable_usage_reporting false

RUN /usr/local/gcloud/google-cloud-sdk/install.sh

# Adding the package path to local
ENV PATH $PATH:/usr/local/gcloud/google-cloud-sdk/bin

# set work directory
WORKDIR /app

# set env variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# install dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt


# copy project
COPY . .

