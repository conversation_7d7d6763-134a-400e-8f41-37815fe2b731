from datetime import datetime
from typing import Optional

from pydantic import BaseModel


class RateConfirmationRequestSchema(BaseModel):
    file_url: str
    is_premium: bool = False


class RateConfirmationVisionRequestSchema(BaseModel):
    file_url: str


class RateConfirmationTextRequestSchema(BaseModel):
    content: str


class RateConfirmationMailRequestSchema(BaseModel):
    file_url: str
    domain: str
    access_token: str


class RateConfirmationAsyncRequestSchema(BaseModel):
    file_urls: list[str]
    file_ids: list[int]
    customers: list[int]
    mc_number: int
    access_token: str
    success_callback_url: str
    failure_callback_url: str
    is_premium: bool = False


class FileSchema(BaseModel):
    id: int
    file: str
    format: str
    name: str
    created_datetime: str


class TaskBatchSchema(BaseModel):
    task_ids: list[str]


class StopSchema(BaseModel):
    company_name: str = None
    address: str = None
    zip_code: str = None
    enter_time: datetime = None
    exit_time: datetime = None
    reference_id: str = None
    seal_number: str = None
    stop_type: str = None


class CustomerAgentSchema(BaseModel):
    agent_name: Optional[str] = None
    agent_phone: Optional[str] = None
    agent_phone_ext: Optional[str] = None
    agent_email: Optional[str] = None


class RateConfirmationSchema(BaseModel):
    load_id: str
    load_pay: float
    customer: Optional[str] = None
    customer_email: Optional[str] = None
    weight: str = None
    goods: str = None
    hazmat: bool
    customer_agent: Optional[CustomerAgentSchema] = None
    file: Optional[FileSchema] = None
    stops: list[StopSchema]
    po_number: Optional[str] = None


class RateConfirmationTaskSuccessCallbackSchema(BaseModel):
    customer: str
    mc_number: str
    filename: str
    load: RateConfirmationSchema


class RateConfirmationTaskFailureCallbackSchema(BaseModel):
    customer: str
    mc_number: str
    filename: str
    error: str
