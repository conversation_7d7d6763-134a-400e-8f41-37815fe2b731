from datetime import datetime

from pydantic import BaseModel


class BillRequestSchema(BaseModel):
    file_url: str


class BillCategoryRequestSchema(BaseModel):
    file_url: str
    categories: str


class BillSchema(BaseModel):
    vendor_name: str
    vendor_address: str
    bill_date: datetime
    transaction_nature: str
    total_pay: float


class BillCategorySchema(BaseModel):
    vendor_name: str
    vendor_address: str
    bill_date: datetime
    transaction_nature: str
    total_pay: float
    category: str
    explanation: str
