from datetime import date
from typing import List

from pydantic import BaseModel


class SafetyRequestSchema(BaseModel):
    file_url: str


class Violation(BaseModel):
    violation_code: str
    violation_description: str


class SafetyResponseSchema(BaseModel):
    report_number: str
    inspection_date: date
    location_state: str
    location_city: str
    location: str
    note: str
    violations: List[Violation]
