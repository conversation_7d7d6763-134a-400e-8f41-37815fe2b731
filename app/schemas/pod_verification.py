from typing import Optional
from pydantic import BaseModel
from typing import List


class PODVerificationRequestSchema(BaseModel):
    file_urls: List[str]


class PODVerificationSchema(BaseModel):
    pickup_address: str
    destination_address: str
    max_page_count: int
    current_page_count: int
    auth_mark_type: int  # 0 = none, 1 = has stamp, 2 = has signature, 3 = has both
    po_number: str


class PODCrosscheckRequestSchema(BaseModel):
    pod_url: str
    original_url: str


class PODCrosscheckVerificationSchema(BaseModel):
    approve: bool
    reason: Optional[str] = ""
    max_page_count: int
    current_page_count: int
    auth_mark_type: int
