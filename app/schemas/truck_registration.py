from datetime import date

from pydantic import BaseModel


class TruckRegistrationRequestSchema(BaseModel):
    file_url: str


class TruckRegistrationSchema(BaseModel):
    vehicle_make: str
    vehicle_model: str
    vehicle_identification_number: str
    vehicle_year: str
    vehicle_plate_number: str
    vehicle_registration_state: str
    vehicle_registration_expiration_date: date
    unit_number: str
    owner_registrant: str
    usdot: str
