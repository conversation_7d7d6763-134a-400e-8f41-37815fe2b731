from datetime import datetime, date

from pydantic import BaseModel


class WorkOrderRequestSchema(BaseModel):
    file_url: str


class WorkOrderPartSerializer(BaseModel):
    item_name: str
    quantity: float
    amount: float
    unit_price: float
    order_type: str


class WorkOrderSchema(BaseModel):
    invoice_id: str
    invoice_date: date | datetime
    vendor_name: str
    vendor_address: str
    vendor_phone: str
    vendor_email: str
    total_price: float
    parts: list[WorkOrderPartSerializer]
