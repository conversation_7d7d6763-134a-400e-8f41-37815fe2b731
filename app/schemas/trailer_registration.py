from datetime import date

from pydantic import BaseModel


class TrailerRegistrationRequestSchema(BaseModel):
    file_url: str


class TrailerRegistrationSchema(BaseModel):
    vehicle_make: str
    vehicle_model: str
    vehicle_identification_number: str
    vehicle_year: str
    vehicle_plate_number: str
    vehicle_registration_state: str
    vehicle_registration_expiration_date: date
    unit_number: str
