import uuid

from fastapi import APIRouter
from fastapi import HTT<PERSON>Exception
from fastapi import BackgroundTasks

from app.models.log import LogTransactionType
from app.scenarios import Scenario
from app.parser.bill import parse_bill, parse_bill_category
from app.schemas.bill import (
    Bill<PERSON>che<PERSON>,
    BillRequestSchema,
    BillCategoryRequestSchema,
    BillCategorySchema,
)
from app.schemas.truckgpt_response import TruckGptServiceResponseType
from app.prompts.bill import INSTRUCTIONS, FUNCTIONS, CATEGORY_INSTRUCTIONS, TOOLS
from app.utils.file import save_file_as_image, download_file, remove_file
from app.utils.content import detect_text_images
from app.services.gpt import truck_gpt, truck_gpt3, truck_gpt_vision
from app.services.log import create_log_record
from app.utils.prompt import get_vision_prompt

router = APIRouter(
    prefix="/bill",
    tags=["bill"],
)


@router.post("/", response_model=BillSchema)
async def _upload_bill(
    request_data: BillRequestSchema, background_tasks: BackgroundTasks
):
    request_id = uuid.uuid4()
    file_path = download_file(request_data.file_url)

    prompt = get_vision_prompt(file_path=file_path, instructions=INSTRUCTIONS)

    response: TruckGptServiceResponseType = truck_gpt_vision(
        prompt=prompt,
        tools=TOOLS,
    )
    log_record: LogTransactionType = {
        "scenario": Scenario.BILL.value,
        "input_file": request_data.file_url,
        "gpt_response": response["raw_response"],
        "request_id": request_id,
        "prompt_text": response["prompt"],
    }
    if not response["success"]:
        log_record["error"] = response["error"]
        background_tasks.add_task(create_log_record, **log_record)
        raise HTTPException(status_code=400, detail="Try again later!")

    result = parse_bill(response["result"])
    log_record["output"] = result
    background_tasks.add_task(create_log_record, **log_record)
    return result


@router.post("/category", response_model=BillCategorySchema)
async def _upload_bill_category(
    request_data: BillCategoryRequestSchema, background_tasks: BackgroundTasks
):
    request_id = uuid.uuid4()
    file_path = download_file(request_data.file_url)

    file_paths = save_file_as_image(file_path, is_file=False)

    remove_file(file_path)

    content = detect_text_images(paths=file_paths)

    response: TruckGptServiceResponseType = truck_gpt(
        instruction=INSTRUCTIONS, functions=FUNCTIONS, content=content
    )

    log_record: LogTransactionType = {
        "scenario": Scenario.BILL.value,
        "input_file": request_data.file_url,
        "gpt_response": response["raw_response"],
        "request_id": request_id,
        "prompt_text": response["prompt"],
    }

    if not response["success"]:
        log_record["error"] = response["error"]
        background_tasks.add_task(create_log_record, **log_record)
        raise HTTPException(status_code=400, detail="Try again later!")

    log_record["output"] = response["result"]
    background_tasks.add_task(create_log_record, **log_record)
    response_category: TruckGptServiceResponseType = truck_gpt3(
        instruction=CATEGORY_INSTRUCTIONS,
        categories=request_data.categories,
        content=content,
    )

    if not response_category["success"]:
        log_record["error"] = response["error"]
        background_tasks.add_task(create_log_record, **log_record)
        raise HTTPException(status_code=400, detail="Try again later!")

    log_record["output"] = response["result"]
    background_tasks.add_task(create_log_record, **log_record)
    result = parse_bill_category(response["result"], response_category["result"])
    log_record["output"] = result
    background_tasks.add_task(create_log_record, **log_record)
    return result
