import uuid
import json

from fastapi import APIRouter
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from fastapi import BackgroundTasks

from fastapi import APIRouter

from app.models.log import LogTransactionType
from app.scenarios import Scenario

from app.schemas.truckgpt_response import TruckGptServiceResponseType
from app.parser.work_order import parse_work_order
from app.schemas.work_order import WorkOrderSchema, WorkOrderRequestSchema
from app.utils.file import save_file_as_image, download_file, remove_file
from app.prompts.work_order import INSTRUCTIONS, TOOLS
from app.utils.content import detect_text_images, get_content
from app.services.gpt import truck_gpt, truck_gpt_vision, truck_gpt_tool
from app.utils.prompt import get_vision_prompt
from app.services.log import create_log_record

router = APIRouter(
    prefix="/work_order",
    tags=["work_order"],
)


@router.post("/", response_model=WorkOrderSchema)
async def _upload_work_order(
        request_data: WorkOrderRequestSchema,
        background_tasks: BackgroundTasks
):
    request_id = uuid.uuid4()
    file_path = download_file(request_data.file_url)

    # content = get_content(file_path=file_path, is_premium=True)
    #
    # response: TruckGptServiceResponseType = truck_gpt_tool(
    #     instruction=INSTRUCTIONS,
    #     content=content,
    #     tools=TOOLS
    # )
    #
    # remove_file(file_path)

    # detect_text_images
    prompt = get_vision_prompt(file_path=file_path, instructions=INSTRUCTIONS)

    # truck_gpt
    response: TruckGptServiceResponseType = truck_gpt_vision(
        prompt=prompt,
        tools=TOOLS,
        max_tokens=4096
    )
    log_record: LogTransactionType = {
        "scenario": Scenario.BILL.value,
        "input_file": request_data.file_url,
        "gpt_response": response["raw_response"],
        "request_id": request_id,
        "prompt_text": response["prompt"]
    }

    if not response['success']:
        log_record["error"] = response['error']
        background_tasks.add_task(create_log_record, **log_record)
        raise HTTPException(status_code=400, detail="Try again later!")

    result = parse_work_order(response['result'])
    log_record["output"] = result
    background_tasks.add_task(create_log_record, **log_record)

    return result
