import json
import uuid
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecut<PERSON>, as_completed

from fastapi import APIRouter, HTTPException
from fastapi import HTTPException
from fastapi import BackgroundTasks

from app.models.log import LogTransactionType
from app.scenarios import Scenario
from app.utils.file import download_file, remove_file
from app.utils.prompt import (
    get_vision_prompt,
    get_vision_and_text_prompt,
)
from app.prompts.rate_confirmation import INSTRUCTIONS, FUNCTIONS
from app.prompts.rate_confirmation_vision import INSTRUCTIONS as INSTRUCTIONS_VISION, TOOLS
from app.utils.content import get_content
from app.tasks.rate_confirmation import task_rate_confirmation, task_rate_confirmation_vision
from app.schemas.truckgpt_response import TruckGptServiceResponseType
from app.schemas.rate_confirmation import (
    RateConfirmationRequestSchema,
    RateConfirmationVisionRequestSchema,
    RateConfirmationTextRequestSchema,
    RateConfirmationMailRequestSchema,
    RateConfirmationAsyncRequestSchema,
    RateConfirmationSchema,
    RateConfirmationTaskSuccessCallbackSchema,
    RateConfirmationTaskFailureCallbackSchema,
)
from app.services.gpt import truck_gpt, truck_gpt_vision, truck_gpt_tool
from app.services.tms import upload_file_to_tms
from app.parser.rate_confirmation import parse_rate_confirmation, parse_rate_confirmation_vision
from app.services.log import create_log_record

router = APIRouter(
    prefix="/rate_confirmation",
    tags=["rate_confirmation"],
)


@router.post("/", response_model=RateConfirmationSchema)
async def _upload_rate_confirmation(
        request_data: RateConfirmationRequestSchema,
        background_tasks: BackgroundTasks
):
    request_id = uuid.uuid4()
    file_path = download_file(request_data.file_url)

    # get_content
    content = get_content(file_path=file_path, is_premium=request_data.is_premium)

    # truck_gpt
    response: TruckGptServiceResponseType = truck_gpt(
        instruction=INSTRUCTIONS,
        functions=FUNCTIONS,
        content=content
    )

    # log_record
    log_record: LogTransactionType = {
        "scenario": Scenario.RATE_CONFIRMATION.value,
        "input_file": request_data.file_url,
        "gpt_response": response["raw_response"],
        "request_id": request_id,
        "prompt_text": response["prompt"]
    }

    if not response['success']:
        log_record["error"] = response['error']
        background_tasks.add_task(create_log_record, **log_record)
        raise HTTPException(status_code=400, detail="Try again later!")
    remove_file(file_path)

    # parse_response
    result = parse_rate_confirmation(response['result'])
    log_record["output"] = result
    background_tasks.add_task(create_log_record, **log_record)
    return result


@router.post("/vision", response_model=RateConfirmationSchema)
def _upload_rate_confirmation_vision(
        request_data: RateConfirmationVisionRequestSchema,
        background_tasks: BackgroundTasks
):
    request_id = uuid.uuid4()
    try:
        file_path = download_file(request_data.file_url)

        # content = get_content(file_path=file_path, is_premium=True)
        #
        # response: TruckGptServiceResponseType = truck_gpt_tool(
        #     instruction=INSTRUCTIONS_VISION,
        #     content=content,
        #     tools=TOOLS
        # )
        #
        # remove_file(file_path)

        # detect_text_images
        prompt = get_vision_and_text_prompt(file_path=file_path, instructions=INSTRUCTIONS_VISION)

        # truck_gpt
        response: TruckGptServiceResponseType = truck_gpt_vision(
            prompt=prompt,
            tools=TOOLS
        )
        log_record: LogTransactionType = {
            "scenario": Scenario.RATE_CONFIRMATION_VISION.value,
            "input_file": request_data.file_url,
            "gpt_response": response["raw_response"],
            "request_id": request_id,
            "prompt_text": response["prompt"]
        }
        if not response['success']:
            log_record["error"] = response['error']
            background_tasks.add_task(create_log_record, **log_record)
            raise HTTPException(status_code=400, detail="Try again later!")
        result = parse_rate_confirmation_vision(response['result'])
        log_record["output"] = result
        background_tasks.add_task(create_log_record, **log_record)
    except Exception as e:
        file_path = download_file(request_data.file_url)
        content = get_content(file_path=file_path, is_premium=True)

        prompt = [
            {"role": "system", "content": INSTRUCTIONS_VISION},
            {"role": "user", "content": f"""Context: {content}"""},
            {"role": "assistant", "content": "Output:"}
        ]

        response: TruckGptServiceResponseType = truck_gpt_vision(
            prompt=prompt,
            tools=TOOLS,
        )
        log_record: LogTransactionType = {
            "scenario": Scenario.RATE_CONFIRMATION_VISION.value,
            "input_file": request_data.file_url,
            "gpt_response": response["raw_response"],
            "request_id": request_id,
            "prompt_text": response["prompt"]
        }
        if not response['success']:
            log_record["error"] = response['error']
            background_tasks.add_task(create_log_record, **log_record)
            raise HTTPException(status_code=400, detail="Try again later!")
        result = parse_rate_confirmation_vision(response['result'])
        log_record["output"] = result
        background_tasks.add_task(create_log_record, **log_record)
    return result


@router.post("/text", response_model=RateConfirmationSchema)
async def _upload_rate_confirmation_text(
        request_data: RateConfirmationTextRequestSchema,
        background_tasks: BackgroundTasks
):
    request_id = uuid.uuid4()
    prompt = [
        {"role": "system", "content": INSTRUCTIONS_VISION},
        {"role": "user", "content": f"""Context: {request_data.content}"""},
        {"role": "assistant", "content": "Output:"}
    ]

    # response: TruckGptServiceResponseType = truck_gpt_tool(
    #     instruction=INSTRUCTIONS_VISION,
    #     content=request_data.content,
    #     tools=TOOLS
    # )

    # truck_gpt
    response: TruckGptServiceResponseType = truck_gpt_vision(
        prompt=prompt,
        tools=TOOLS
    )
    log_record: LogTransactionType = {
        "scenario": Scenario.RATE_CONFIRMATION_VISION.value,
        "gpt_response": response["raw_response"],
        "request_id": request_id,
        "prompt_text": response["prompt"],
        "input_file": "",
    }
    if not response['success']:
        log_record["error"] = response['error']
        background_tasks.add_task(create_log_record, **log_record)
        raise HTTPException(status_code=400, detail="Try again later!")
    # parse_response
    result = parse_rate_confirmation(response['result'])
    log_record["output"] = result
    background_tasks.add_task(create_log_record, **log_record)
    return result


@router.post("/mail", response_model=RateConfirmationSchema)
async def _upload_rate_confirmation_mail(
        request_data: RateConfirmationMailRequestSchema,
        background_tasks: BackgroundTasks
):
    request_id = uuid.uuid4()
    try:
        file_path = download_file(request_data.file_url)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

    prompt = get_vision_prompt(file_path=file_path, instructions=INSTRUCTIONS_VISION, is_remove_file=False)

    # Use ThreadPoolExecutor to run tasks concurrently
    with ThreadPoolExecutor() as executor:
        future_tms = executor.submit(
            upload_file_to_tms, file_path=file_path, domain=request_data.domain, access_token=request_data.access_token
        )
        future_openai = executor.submit(
            truck_gpt_vision, prompt=prompt, tools=TOOLS
        )

        # Wait for both tasks to complete
        for future in as_completed([future_tms, future_openai]):
            if future == future_tms:
                response_from_tms = future.result()
            elif future == future_openai:
                response: TruckGptServiceResponseType = future.result()
    log_record: LogTransactionType = {
        "scenario": Scenario.RATE_CONFIRMATION_EMAIL.value,
        "input_file": request_data.file_url,
        "gpt_response": response["raw_response"],
        "request_id": request_id,
        "prompt_text": response["prompt"]
    }
    if not response['success']:
        log_record["error"] = response['error']
        background_tasks.add_task(create_log_record, **log_record)
        raise HTTPException(status_code=400, detail="Try again later!")
    # parse_response
    result = parse_rate_confirmation(response['result'])
    log_record["output"] = result
    background_tasks.add_task(create_log_record, **log_record)
    result['file'] = response_from_tms

    # Remove the temporary file
    remove_file(file_path)

    return result


@router.post("/async")
async def _upload_rate_confirmation_async(
        request_data: RateConfirmationAsyncRequestSchema,
):
    file_urls = request_data.file_urls
    file_ids = request_data.file_ids
    customers = request_data.customers
    if len(file_urls) != len(customers) != len(file_ids):
        raise HTTPException(status_code=400, detail="Files, customers and file_ids must be the same length")

    task_ids = []

    for file_url, file_id, customer in zip(file_urls, file_ids, customers):
        task_id = task_rate_confirmation.delay(
            file_url=file_url,
            file_id=file_id,
            customer=customer,
            mc_number=request_data.mc_number,
            access_token=f'Bearer {request_data.access_token}',
            success_callback_url=request_data.success_callback_url,
            failure_callback_url=request_data.failure_callback_url,
            is_premium=request_data.is_premium
        )
        task_ids.append(task_id.id)

    return {
        'task_ids': task_ids
    }


@router.post("/vision/async")
async def _upload_rate_confirmation_vision_async(
        request_data: RateConfirmationAsyncRequestSchema,
):
    file_urls = request_data.file_urls
    file_ids = request_data.file_ids
    customers = request_data.customers

    if len(file_urls) != len(customers) != len(file_ids):
        raise HTTPException(status_code=400, detail="Files, customers and file_ids must be the same length")

    task_ids = []

    for file_url, file_id, customer in zip(file_urls, file_ids, customers):
        task_id = task_rate_confirmation_vision.delay(
            file_url=file_url,
            file_id=file_id,
            customer=customer,
            mc_number=request_data.mc_number,
            access_token=f'Bearer {request_data.access_token}',
            success_callback_url=request_data.success_callback_url,
            failure_callback_url=request_data.failure_callback_url
        )
        task_ids.append(task_id.id)

    return {
        'task_ids': task_ids
    }


@router.get("/{task_id}")
async def _get_rate_confirmation(
        task_id: str,
):
    task = task_rate_confirmation.AsyncResult(task_id)
    if task.state == 'SUCCESS':
        return {
            'task_id': task_id,
            'state': task.state,
            'result': task.result
        }
    elif task.state == 'PENDING':
        return {
            'task_id': task_id,
            'state': task.state,
        }
    else:
        return {
            'task_id': task_id,
            'state': task.state,
            'error': task.info
        }


@router.post("/success")
async def _success_callback_rate_confirmation(
        request_data: RateConfirmationTaskSuccessCallbackSchema
):
    print("SUCCESS CALLBACK")
    return {
        'success': True
    }


@router.post("/failure")
async def _failure_callback_rate_confirmation(
        request_data: RateConfirmationTaskFailureCallbackSchema
):
    print("FAILURE CALLBACK")
    return {
        'success': True
    }
