import uuid
import json

from fastapi import APIRouter
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from fastapi import BackgroundTasks

from app.models.log import LogTransactionType
from app.scenarios import Scenario
from app.schemas.truckgpt_response import TruckGptServiceResponseType
from app.parser.trailer_registration import parse_trailer_registration
from app.schemas.trailer_registration import TrailerRegistrationSchema, TrailerRegistrationRequestSchema
from app.utils.file import save_file_as_image, download_file, remove_file
from app.prompts.trailer_registration import INSTRUCTIONS, TOOLS
from app.utils.content import detect_text_images
from app.services.gpt import truck_gpt, truck_gpt_vision
from app.services.log import create_log_record
from app.utils.prompt import get_vision_prompt, get_vision_and_text_prompt

router = APIRouter(
    prefix="/trailer_registration",
    tags=["trailer_registration"],
)


@router.post("/", response_model=TrailerRegistrationSchema)
async def _upload_trailer_registration(
        request_data: TrailerRegistrationRequestSchema,
        background_tasks: BackgroundTasks
):
    request_id = uuid.uuid4()
    file_path = download_file(request_data.file_url)

    prompt = get_vision_and_text_prompt(file_path=file_path, instructions=INSTRUCTIONS)

    response: TruckGptServiceResponseType = truck_gpt_vision(
        prompt=prompt,
        tools=TOOLS,
        max_tokens=4096
    )
    log_record: LogTransactionType = {
        "scenario": Scenario.TRAILER_REGISTRATION.value,
        "input_file": request_data.file_url,
        "gpt_response": response["raw_response"],
        "request_id": request_id,
        "prompt_text": response["prompt"]
    }
    if not response['success']:
        log_record["error"] = response['error']
        background_tasks.add_task(create_log_record, **log_record)
        raise HTTPException(status_code=400, detail="Try again later!")

    result = parse_trailer_registration(response['result'])
    log_record["output"] = result
    background_tasks.add_task(create_log_record, **log_record)
    return result
