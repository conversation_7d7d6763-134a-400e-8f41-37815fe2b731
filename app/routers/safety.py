import uuid
import json

from fastapi import APIRouter
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from fastapi import BackgroundTasks

from app.models.log import LogTransactionType
from app.scenarios import Scenario
from app.schemas.truckgpt_response import TruckGptServiceResponseType
from app.parser.safety import parse_safety
from app.prompts.safety import INSTRUCTIONS, TOOLS
from app.schemas.safety import SafetyRequestSchema, SafetyResponseSchema
from app.services.gpt import truck_gpt_vision, truck_gpt_tool
from app.utils.content import get_content
from app.utils.file import download_file, remove_file
from app.utils.prompt import get_vision_prompt
from app.services.log import create_log_record

router = APIRouter(
    prefix="/safety",
    tags=["safety"],
)


@router.post("/", response_model=SafetyResponseSchema)
async def upload_safety(
    request_data: SafetyRequestSchema, background_tasks: BackgroundTasks
):
    request_id = uuid.uuid4()
    file_path = download_file(request_data.file_url)

    # content = get_content(file_path=file_path, is_premium=True)
    #
    # response: TruckGptServiceResponseType = truck_gpt_tool(
    #     instruction=INSTRUCTIONS, content=content, tools=TOOLS
    # )
    #
    # remove_file(file_path)

    # detect_text_images
    prompt = get_vision_prompt(file_path=file_path, instructions=INSTRUCTIONS)

    # truck_gpt
    response: TruckGptServiceResponseType = truck_gpt_vision(prompt=prompt, tools=TOOLS)
    log_record: LogTransactionType = {
        "scenario": Scenario.SAFETY.value,
        "input_file": request_data.file_url,
        "gpt_response": response["raw_response"],
        "request_id": request_id,
        "prompt_text": response["prompt"],
    }
    if not response["success"]:
        log_record["error"] = response["error"]
        background_tasks.add_task(create_log_record, **log_record)
        raise HTTPException(status_code=400, detail="Try again later!")
    result = parse_safety(response["result"])
    log_record["output"] = result
    background_tasks.add_task(create_log_record, **log_record)

    return result
