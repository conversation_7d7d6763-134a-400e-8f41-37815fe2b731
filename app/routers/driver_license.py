import uuid
import json
from fastapi import APIRouter
from fastapi import HTT<PERSON>Ex<PERSON>
from fastapi import BackgroundTasks

from app.models.log import LogTransactionType
from app.scenarios import Scenario
from app.prompts.driver_license import INSTRUCTIONS, TOOLS
from app.parser.driver_license import parse_driver_license
from app.schemas.driver_license import DriverLicenseSchema, DriverLicenseRequestSchema
from app.schemas.truckgpt_response import TruckGptServiceResponseType
from app.utils.file import save_file_as_image, download_file, remove_file
from app.utils.content import detect_text_images
from app.services.gpt import truck_gpt, truck_gpt_vision
from app.services.log import create_log_record
from app.utils.prompt import get_vision_prompt, get_vision_and_text_prompt

router = APIRouter(
    prefix="/driver_license",
    tags=["driver_license"],
)


@router.post("/", response_model=DriverLicenseSchema)
async def _upload_driver_license(
        request_data: DriverLicenseRequestSchema,
        background_tasks: BackgroundTasks
):
    request_id = uuid.uuid4()
    file_path = download_file(request_data.file_url)

    prompt = get_vision_and_text_prompt(file_path=file_path, instructions=INSTRUCTIONS)

    response: TruckGptServiceResponseType = truck_gpt_vision(
        prompt=prompt,
        tools=TOOLS,
        max_tokens=4096
    )
    log_record: LogTransactionType = {
        "scenario": Scenario.DRIVER_LICENSE.value,
        "input_file": request_data.file_url,
        "gpt_response": response["raw_response"],
        "request_id": request_id,
        "prompt_text": response["prompt"],
    }

    if not response["success"]:
        log_record["error"] = response["error"]
        background_tasks.add_task(create_log_record, **log_record)
        raise HTTPException(status_code=400, detail="Try again later!")

    result = parse_driver_license(response["result"])
    log_record["output"] = result
    background_tasks.add_task(create_log_record, **log_record)
    return result
