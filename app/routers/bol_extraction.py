import json
import uuid

from fastapi import APIRouter, HTTPException
from fastapi import HTTPException
from fastapi import BackgroundTasks

from app.models.log import LogTransactionType
from app.scenarios import Scenario
from app.utils.file import download_file, remove_file
from app.utils.image_quality import load_all_images
from app.utils.prompt import get_vision_text_prompts
from app.prompts.bol_extraction import INSTRUCTIONS, TOOLS
from app.utils.content import get_content_from_files
from app.schemas.truckgpt_response import TruckGptServiceResponseType
from app.schemas.bol_extraction import (
    BOLExtractionRequestSchema,
    BOLExtractionSchema
)
from app.services.gpt import truck_gpt_vision
from app.parser.bol_extraction import parse_bol_extraction
from app.services.log import create_log_record

router = APIRouter(
    prefix="/bol-extraction",
    tags=["bol-extraction"],
)


@router.post("/", response_model=BOLExtractionSchema)
def _upload_bol_doc(
        request_data: <PERSON><PERSON>ExtractionRequestSchema,
        background_tasks: BackgroundTasks
):
    request_id = str(uuid.uuid4())
    file_paths = []
    for url in request_data.file_urls:
        file_paths.append(download_file(url))

    try:

        images = load_all_images(file_paths)

        # detect_text_images
        prompt = get_vision_text_prompts(
            images=images, instructions=INSTRUCTIONS)

        # truck_gpt
        response: TruckGptServiceResponseType = truck_gpt_vision(
            prompt=prompt,
            tools=TOOLS
        )
        log_record: LogTransactionType = {
            "scenario": Scenario.BOL_EXTRACTION.value,
            "input_file": ", ".join(file_paths),
            "gpt_response": response["raw_response"],
            "request_id": request_id,
            "prompt_text": response["prompt"]
        }
        if not response['success']:
            log_record["error"] = response['error']
            background_tasks.add_task(create_log_record, **log_record)
            raise HTTPException(status_code=400, detail="Try again later!")
        result = parse_bol_extraction(response['result'])
        log_record["output"] = result
        background_tasks.add_task(create_log_record, **log_record)
    except Exception as e:
        content = get_content_from_files(
            file_paths=file_paths, is_premium=True)

        prompt = [
            {"role": "system", "content": INSTRUCTIONS},
            {"role": "user", "content": f"""Context: {content}"""},
            {"role": "assistant", "content": "Output:"}
        ]

        response: TruckGptServiceResponseType = truck_gpt_vision(
            prompt=prompt,
            tools=TOOLS,
        )
        log_record: LogTransactionType = {
            "scenario": Scenario.BOL_EXTRACTION.value,
            "input_file": ", ".join(file_paths),
            "gpt_response": response["raw_response"],
            "request_id": request_id,
            "prompt_text": response["prompt"]
        }
        if not response['success']:
            log_record["error"] = response['error']
            background_tasks.add_task(create_log_record, **log_record)
            raise HTTPException(status_code=400, detail="Try again later!")
        result = parse_bol_extraction(response['result'])
        log_record["output"] = result
        background_tasks.add_task(create_log_record, **log_record)

    finally:
        for path in file_paths:
            remove_file(path)

    return result
