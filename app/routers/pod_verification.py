import json
import uuid
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed

from fastapi import APIRouter, HTTPException
from fastapi import BackgroundTasks
from fastapi import File, UploadFile
from typing import List


from app.models.log import LogTransactionType
from app.scenarios import <PERSON><PERSON><PERSON>
from app.services.s3 import delete_file_from_s3_url
from app.utils.file import download_file, remove_file
from app.utils.image_quality import extract_images_and_upload, process_document_quality, load_all_images
from app.utils.prompt import get_vision_text_prompt_with_ocr, get_crosscheck_prompt
from app.prompts.pod_verification import INSTRUCTIONS, TOOLS
from app.prompts.pod_crosscheck import INSTRUCTION_CROSSCHECK, TOOLS_CROSSCHECK
from app.utils.content import get_content, get_content_from_files
from app.tasks.pod_verification import task_pod_verification
from app.schemas.truckgpt_response import TruckGptServiceResponseType
from app.schemas.pod_verification import (
    PODVerificationSchema,
    PODVerificationRequestSchema,
    PODCrosscheckVerificationSchema,
    PODCrosscheckRequestSchema
)
from app.services.gpt import truck_gpt_vision
from app.parser.pod_verification import parse_pod_verification, parse_pod_crosscheck_verification
from app.services.log import create_log_record


router = APIRouter(
    prefix="/pod-verification",
    tags=["pod-verification"],
)


@router.post("/", response_model=PODVerificationSchema)
async def _upload_pod_verification(
        request_data: PODVerificationRequestSchema,
        background_tasks: BackgroundTasks
):
    request_id = str(uuid.uuid4())

    file_paths = []
    for url in request_data.file_urls:
        file_paths.append(download_file(url))

    try:
        # Step 1: Load images only once
        images = load_all_images(file_paths)

        # Step 2: Run quality check and get OCR text
        quality_result, ocr_per_page = process_document_quality(images)

        if not quality_result["approve"]:
            fail_reasons = "; ".join(
                f"Page {r['page']}: {r['fail_reason']}" for r in quality_result["results"] if r.get("fail_reason")
            )

            raise HTTPException(
                status_code=400,
                detail=f"Document failed quality check. Reasons: {fail_reasons or 'Unknown reason.'}"
            )

        # Step 3: Build the prompt using images and OCR text
        prompt = get_vision_text_prompt_with_ocr(
            images=images,
            instructions=INSTRUCTIONS,
            per_page_ocr=ocr_per_page
        )

        # Step 4: Call GPT with the prompt and tools
        response: TruckGptServiceResponseType = truck_gpt_vision(
            prompt=prompt,
            tools=TOOLS
        )

        log_record: LogTransactionType = {
            "scenario": Scenario.POD_VERIFICATION.value,
            "input_file": ", ".join(file_paths),
            "gpt_response": response["raw_response"],
            "request_id": request_id,
            "prompt_text": response["prompt"]
        }

        if not response['success']:
            log_record["error"] = response['error']
            background_tasks.add_task(create_log_record, **log_record)
            raise HTTPException(status_code=400, detail="Try again later!")

        result = parse_pod_verification(response['result'])
        log_record["output"] = result
        background_tasks.add_task(create_log_record, **log_record)

    except HTTPException as e:
        # Re-raise HTTPException for client errors
        raise e
    except Exception as e:
        # Fallback: Use text-only OCR content if image flow fails
        content = get_content_from_files(
            file_paths=file_paths, is_premium=True)
        prompt = [
            {"role": "system", "content": INSTRUCTIONS},
            {"role": "user", "content": f"Context: {content}"},
            {"role": "assistant", "content": "Output:"}
        ]

        response: TruckGptServiceResponseType = truck_gpt_vision(
            prompt=prompt,
            tools=TOOLS
        )

        log_record: LogTransactionType = {
            "scenario": Scenario.POD_VERIFICATION.value,
            "input_file": ", ".join(file_paths),
            "gpt_response": response["raw_response"],
            "request_id": request_id,
            "prompt_text": response["prompt"]
        }

        if not response['success']:
            log_record["error"] = response['error']
            background_tasks.add_task(create_log_record, **log_record)
            raise HTTPException(status_code=400, detail="Try again later!")

        result = parse_pod_verification(response['result'])
        log_record["output"] = result
        background_tasks.add_task(create_log_record, **log_record)

    finally:
        for path in file_paths:
            remove_file(path)

    return result


@router.post("/crosscheck", response_model=PODCrosscheckVerificationSchema)
async def _upload_pod_crosscheck(
    request_data: PODCrosscheckRequestSchema,
    background_tasks: BackgroundTasks
):
    request_id = str(uuid.uuid4())

    pod_images_urls = extract_images_and_upload(
        request_data.pod_url, request_id)
    original_doc_images_urls = extract_images_and_upload(
        request_data.original_url, request_id, isPOD=False)

    temp_uploaded_urls = pod_images_urls + original_doc_images_urls

    try:
        # Step 1: Build prompt directly from URLs
        prompt = get_crosscheck_prompt(
            pod_urls=pod_images_urls,
            original_urls=original_doc_images_urls,
            instructions=INSTRUCTION_CROSSCHECK,
        )

        # Step 2: Call GPT with prompt and tools
        response: TruckGptServiceResponseType = truck_gpt_vision(
            prompt=prompt,
            tools=TOOLS_CROSSCHECK,
        )

        log_record = {
            "scenario": Scenario.POD_CROSSCHECK.value,
            "input_file": f"POD: {request_data.pod_url}, ORG: {request_data.original_url}",
            "gpt_response": response["raw_response"],
            "request_id": request_id,
            "prompt_text": response["prompt"]
        }

        if not response["success"]:
            log_record["error"] = response["error"]
            background_tasks.add_task(
                delete_file_from_s3_url, json.dumps(temp_uploaded_urls))
            background_tasks.add_task(create_log_record, **log_record)
            raise HTTPException(status_code=400, detail="Try again later!")

        result = parse_pod_crosscheck_verification(response["result"])
        log_record["output"] = result
        background_tasks.add_task(
            delete_file_from_s3_url, json.dumps(temp_uploaded_urls))
        background_tasks.add_task(create_log_record, **log_record)

    except Exception as e:
        background_tasks.add_task(
            delete_file_from_s3_url, json.dumps(temp_uploaded_urls))
        raise HTTPException(status_code=500, detail=str(e))

    return result
