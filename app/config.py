import os

from PIL import Image
from dotenv import load_dotenv, find_dotenv
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker

load_dotenv(find_dotenv())

ENVIRONMENT = os.getenv("ENVIRONMENT", "development")

SENTRY_DSN = os.getenv("SENTRY_DSN", "")

REDIS_URI = os.getenv("REDIS_URI", "")
CELERY_BROKER_URI = os.getenv("CELERY_BROKER_URI", "")

OPENAI_URL = os.getenv("OPENAI_URL", "")
OPENAI_MODEL = os.getenv("OPENAI_MODEL", "")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
OPENAI_API_VERSION = os.getenv("OPENAI_API_VERSION", "")

OPENAI_VISION_URL = os.getenv("OPENAI_VISION_URL", "")
OPENAI_VISION_MODEL = os.getenv("OPENAI_VISION_MODEL", "")
OPENAI_VISION_API_KEY = os.getenv("OPENAI_VISION_API_KEY", "")
OPENAI_VISION_API_VERSION = os.getenv("OPENAI_VISION_API_VERSION", "")

OPENAI_CATEGORY_URL = os.getenv("OPENAI_CATEGORY_URL", "")
OPENAI_CATEGORY_MODEL = os.getenv("OPENAI_CATEGORY_MODEL", "")
OPENAI_CATEGORY_API_KEY = os.getenv("OPENAI_CATEGORY_API_KEY", "")
OPENAI_CATEGORY_API_VERSION = os.getenv("OPENAI_CATEGORY_API_VERSION", "")
DATABASE_URL = os.getenv("DATABASE_URL", "")

# AWS
AWS_STORAGE_BUCKET_NAME = os.getenv(
    'AWS_STORAGE_BUCKET_NAME', 'truckgpt')
AWS_S3_REGION_NAME = os.getenv('AWS_S3_REGION_NAME', 'us-east-1')
AWS_DEFAULT_ACL = os.getenv('AWS_DEFAULT_ACL', 'public-read')

# Image Quality Assessment Thresholds
IMAGE_QUALITY_MIN_OCR_CONFIDENCE = float(
    os.getenv("IMAGE_QUALITY_MIN_OCR_CONFIDENCE", "85.0"))
IMAGE_QUALITY_MIN_PAGE_CONFIDENCE = float(
    os.getenv("IMAGE_QUALITY_MIN_PAGE_CONFIDENCE", "70.0"))

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "vision_key.json"
Image.MAX_IMAGE_PIXELS = None


engine = create_engine(DATABASE_URL)
metadata = MetaData()
session = sessionmaker(bind=engine, autoflush=False, autocommit=False)
