from datetime import datetime


INSTRUCTIONS = f"""
## INSTRUCTIONS:
- You are an AI assistant that extracts load/shipment/order id, customer, weight, goods, total pay and the details of each pickup/delivery/stop from the context.
- You will be provided both image and text versions of rate confirmation PDF. So, please extract the most accurate data using both images and text.
- Customer company name is for the whole rate confirmation document and usually customer company name is located on the top-left or top-right of the document. Remember: This is NOT a carrier company name.
- Take Load ID/number/# when both Load and Order ID/number/# are available.
- Take Order ID/number/# when both Order and Shipment ID/number/# are available.
- load/shipment ID/number/# may contain both letters and digits.
- Ignore identifiers like P/U#, BOL#, Ref#, PO#, Delivery or Pickup Number unless explicitly labeled as Load ID/number/#, Order ID/number/#, or Shipment ID/number/#.
- When extracting load/shipment/order IDs, ignore any prefix labels and extract only the clean numeric/alphanumeric identifier.
- If the ID includes a "#" symbol, extract the full alphanumeric ID after that symbol and ignore any labels.
- Total pay field MUST contain numbers only.
- Cargo value is not the total pay.
- List pickup/delivery/stop/shipper/consignee in the order you receive them in the context.
- Usually, company email comes and given for invoices purposes. 
- Customer agent's phone number MUST be in the format (XXX) XXX-XXXX.
- Customer agent's phone extension is a short digit-only value (1–5 digits); if no such extension exists, return an empty string.
- List pickup/delivery/stop in the order you receive them in the context.
- appointment date MUST be in the format mm/dd/yyyy.
- appointment time MUST be in the format HH:MM.
- earlies/latest hours MUST be in the format HH:MM - HH:MM.
- earlies/latest dates MUST be in the format mm/dd/yyyy - mm/dd/yyyy.
- Current year is {datetime.today().year}, so put current year if input context does not have a year.
- If one of the attributes is not presented, give empty string for that attribute.
- Total pay MUST be for the load/freight.
- Place name MUST contain actual place name only, nothing else.
- Extract Purchase Order (PO) Number if labeled as: “PO #”, “Purchase Order #”, “P.O.”, “P.O. #”, “Order #”, “Ref #”, “Reference #”, “DEL #”, or “Pickup #”.
- PO number can be a mix of letters and digits, or a standalone number near such labels.
- If PO number is not found, return an empty string.
- DON'T output the word "UNKNOWN".
- The output has to be in JSON format.
"""

TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "extract_details",
            "description": "Extract load/shipment/order id, customer, weight, goods, total pay and the details of each pickup/delivery/stop in the order you receive them in the context",
            "parameters": {
                "type": "object",
                "properties": {
                    "load/shipment id": {
                        "type": "string",
                        "description": "load/shipment/order # id of Rate Confirmation",
                    },
                    "customer": {
                        "type": "string",
                        "description": "customer company name who owns the load and usually customer company name comes on the top-left or top-right of the document. Remember: This is NOT a carrier company name.",
                    },
                    "customer_email": {
                        "type": "string",
                        "description": "customer company name email - it usually comes for invoices purposes",
                    },
                    "weight": {
                        "type": "string",
                        "description": "weight of the load in lbs",
                    },
                    "goods": {
                        "type": "string",
                        "description": "goods or commodity - information about items that are being carried",
                    },
                    "total pay": {
                        "type": "string",
                        "description": "Total rate pay for the load or rate confirmation",
                    },
                    "hazmat": {
                        "type": "boolean",
                        "description": "If load contain hazardous material: True or False",
                    },
                    "customer_agent": {
                        "type": "object",
                        "description": "Details of the agent from the customer company",
                        "properties": {
                            "agent_name": {
                                "type": "string",
                                "description": "The name of the agent from the customer company"
                            },
                            "agent_phone": {
                                "type": "string",
                                "description": "Phone number of the agent from the customer company"
                            },
                            "agent_phone_ext": {
                                "type": "string",
                                "description": "Phone number extension of the agent from the customer company"
                            },
                            "agent_email": {
                                "type": "string",
                                "description": "Email address of the agent from the customer company"
                            }
                        },
                        "required": ["agent_name", "agent_phone", "agent_email"]
                    },
                    "stops": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "place name": {
                                    "type": "string",
                                    "description": "The name of the place. Place name MUST contain actual place name only, nothing else.",
                                },
                                "apt/unit, street, city, state, zip code address": {
                                    "type": "string",
                                    "description": "apt/unit, street, city, state, zip code address of the place or pickup/delivery/stop.",
                                },
                                "appointment date": {
                                    "type": "string",
                                    "description": "The appointment date for the load that needs to be picked up or delivered.",
                                },
                                "appointment time": {
                                    "type": "string",
                                    "description": "The appointment time for the load that needs to be picked up or delivered in the format HH:MM.",
                                },
                                "earlies/latest hours": {
                                    "type": "string",
                                    "description": "Earliest and latest hours represent hours window that the load can be delivered and it MUST be in the format  HH:MM - HH:MM.",
                                },
                                "earlies/latest dates": {
                                    "type": "string",
                                    "description": "Earliest and latest dates represent dates window that the load can be delivered and it MUST be in the format mm/dd/yyyy - mm/dd/yyyy.",
                                },
                            },
                            "required": [
                                "place name",
                                "apt/unit, street, city, state, zip code address",
                                "appointment date",
                                "appointment time",
                            ],
                        },
                    },
                    "po_number": {
                        "type": "string",
                        "description": "Purchase Order number or equivalent (PO #, Ref #, DEL #, etc.), or empty string if not found."
                    }
                },
                "required": [
                    "load/shipment id",
                    "customer",
                    "customer_email",
                    "weight",
                    "goods",
                    "total pay",
                    "hazmat",
                    "stops",
                    "po_number",
                ],
            },
        },
    }
]
