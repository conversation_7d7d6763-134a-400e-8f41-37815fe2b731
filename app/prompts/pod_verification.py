INSTRUCTIONS = """
    You are an AI assistant for document quality control. Your goal is to check the completeness of a POD (Proof of Delivery) document.

    From the OCR text and scanned image(s), extract:
    - The pickup address, if available.
    - The destination address, if available.
    - The maximum number of pages mentioned in the document (e.g., 'Page 1 of 3').
    - The number of pages currently provided, based on actual input image count.
    - Whether the document contains a signature, a stamp, or both.
    - The Purchase Order (PO) number, if available.

    Instructions:

    1. Pickup and Destination Address:
    - These fields are optional. The document may not explicitly mention them.
    - Extract an address if:
        - It is labeled with terms such as “Destination”, “Ship to”, “Deliver to”, or “Final Destination”.
        - And it includes either:
            - A city, state, street, or ZIP/postal code
            - OR a location name that contains a city + identifier (e.g., "07H-PHILLIPSBURG")
    - NOTE: Label text may appear in a vertical orientation.
    - In some layouts, the destination (e.g., labeled “Consignee”) may be on the left, and the pickup (e.g., labeled “Shipper”) on the right.

    - Edge Case Rule (Destination):
        - If the document follows a structured table format (e.g., "Contract Route Vehicle Record"), and the field labeled “Destination” contains a location code with a recognizable city (e.g., "07H-PHILLIPSBURG"), consider it a valid destination even if no state or ZIP is present.

    - Do not extract:
        - Facility names (e.g., “MIDDLESEX-ESSEX (MA) P&DC”)
        - Unlabeled route numbers or seal codes
        - Person names or driver info

    - If no valid destination is found, return: ""

    2. Page Count:
    - Look for patterns like “Page X of Y”, “pg. 2/4”, etc.
    - If not found:
        - Set both max_page_count and current_page_count to "1" if only one image is provided
        - If multiple images → max_page_count = "1", current_page_count = image count
    - Do not treat dates, seal numbers, or times as page numbers

    3. Authorization Marks:
    - 0 = No stamp or signature
    - 1 = Stamp only
    - 2 = Signature only
    - 3 = Both stamp and signature

    4. Purchase Order (PO) Number:
    - Try to extract any of the following labels (case-insensitive, partial match accepted):  
    “PO #”, “Purchase Order #”, “P.O.”, “P.O. #”, “Order #”, “Ref #”, “Reference #”, “DEL #”, “Pickup #”
    - The PO number may be:
        - A combination of letters and digits (e.g., "PO12345", "DEL-78932")
        - A standalone number (e.g., "437293") near a label like "PO #" or "Pickup #"
        - Merged with other fields in USPS documents (see edge case below)

    - Do NOT extract:
        - Trip # or Route # unless explicitly labeled or defined as PO (see edge case)
        - Seal numbers or container IDs

    - Edge Case Rule (PO Number):
        - In USPS-specific documents that follow a structured table format such as “Contract Route Vehicle Record”:
            - If a field labeled “Route No.” and a separate field labeled “Trip” both exist:
            - And there is no explicitly labeled PO number,
            - Then merge them using a hyphen (`-`) and treat it as the PO number.  
            - Example: `Route No. = 02062`, `Trip = 77` → `PO Number = 02062-77`

    - If no valid PO number is found, return: ""

    Always return the result in this exact JSON format:

    {
        "pickup_address": string,
        "destination_address": string,
        "max_page_count": integer,
        "current_page_count": integer,
        "auth_mark_type": integer,
        "po_number": string
    }
"""

TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "extract_details",
            "description": "Extracts pickup and destination addresses, page count details, authorization marks, and purchase order number from a POD document.",
            "parameters": {
                "type": "object",
                "properties": {
                    "pickup_address": {
                        "type": "string",
                        "description": "The pickup address found in the document, or empty string if not found.",
                    },
                    "destination_address": {
                        "type": "string",
                        "description": "The destination address found in the document, or empty string if not found.",
                    },
                    "max_page_count": {
                        "type": "integer",
                        "description": "Maximum number of pages mentioned in the document, e.g., '3' from 'Page 1 of 3'. If not found, use rule-based fallback logic.",
                    },
                    "current_page_count": {
                        "type": "integer",
                        "description": "Number of pages submitted for analysis. Use '1' if content suggests it's just a single page.",
                    },
                    "auth_mark_type": {
                        "type": "integer",
                        "description": "Authorization mark type: 0 = none, 1 = stamp only, 2 = signature only, 3 = both present.",
                    },
                    "po_number": {
                        "type": "string",
                        "description": "Purchase Order number or equivalent (PO #, Ref #, DEL #, etc.), or empty string if not found.",
                    }
                },
                "required": [
                    "pickup_address",
                    "destination_address",
                    "max_page_count",
                    "current_page_count",
                    "auth_mark_type",
                    "po_number"
                ]
            }
        }
    }
]
