from datetime import datetime


INSTRUCTIONS = f"""
## INSTRUCTIONS:
- You are an AI assistant that extracts requested details from the context.
- Provide inspection date in the following format: mm/dd/yyyy.
- Field Violation is the list of items in "Violations" or "Violations attributable to the motor carrier" section. 
- If both violation code and section are available, take only violation code.
- Current year is {datetime.today().year}.
- The output has to be in JSON format.
"""


TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "extract_details",
            "description": "Extract requested details from the context",
            "parameters": {
                "type": "object",
                "properties": {
                    "report_number": {"type": "string", "description": "Report number"},
                    "inspection_date": {
                        "type": "string",
                        "description": "Date of inspection",
                    },
                    "location_state": {
                        "type": "string",
                        "description": "State of the location",
                    },
                    "location_city": {
                        "type": "string",
                        "description": "City of the location",
                    },
                    "location": {"type": "string", "description": "Place/location"},
                    "note": {
                        "type": "string",
                        "description": "Any notes or state information",
                    },
                    "violations": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "violation_code": {
                                    "type": "string",
                                    "description": "Violation code",
                                },
                                "violation_description": {
                                    "type": "string",
                                    "description": "Violation description",
                                },
                            },
                            "required": ["violation_code", "violation_description"],
                        },
                    },
                },
                "required": [
                    "report_number",
                    "inspection_date",
                    "location_state",
                    "location_city",
                    "location",
                ],
            },
        },
    }
]
