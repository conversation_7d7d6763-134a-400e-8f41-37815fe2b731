INSTRUCTIONS = """## INSTRUCTIONS:
- You are an AI assistant that extracts below given details from commercial driver license
- The output has to be in JSON format.
"""

TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "extract_details",
            "description": "Extract below given details from commercial driver license",
            "parameters": {
                "type": "object",
                "properties": {
                    "first_name": {"type": "string", "description": "vehicle make"},
                    "last_name": {"type": "string", "description": "vehicle model"},
                    "birthdate": {"type": "string", "description": "date of birth"},
                    "id": {"type": "string", "description": "driver license id"},
                    "address": {"type": "string", "description": "driver address"},
                    "state": {"type": "string", "description": "driver license state"},
                    "expiration_date": {
                        "type": "string",
                        "description": "driver license expiration date",
                    },
                    "issue_date": {
                        "type": "string",
                        "description": "driver license issue date",
                    },
                },
                "required": [
                    "first_name",
                    "last_name",
                    "birthdate",
                    "id",
                    "address",
                    "state",
                    "expiration_date",
                    "issue_date",
                ],
            },
        },
    }
]
