INSTRUCTIONS = """
## INSTRUCTIONS:
- You are an AI assistant that extracts requested details from the context.
- Vendor name is usually vehicle assistance company.
- Provide phone number in the following format: (xxx) xxx-xxxx.
- Provide invoice date in the following format: dd/mm/yyyy.
- If vendor email is not provided in the content, then output empty string.
- You MUST list such items in parts/labors where EXTENDED AMOUNT is NOT ZERO value.
- The output has to be in JSON format.
"""

TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "extract_details",
            "description": "Extract requested details from the context",
            "parameters": {
                "type": "object",
                "properties": {
                    "invoice id/number": {
                        "type": "string",
                        "description": "invoice id/number"
                    },
                    "invoice date": {
                        "type": "string",
                        "description": "invoice date"
                    },
                    "vendor name": {
                        "type": "string",
                        "description": "vendor name that provided the service"
                    },
                    "vendor address": {
                        "type": "string",
                        "description": "vendor address that provided the service"
                    },
                    "vendor phone": {
                        "type": "string",
                        "description": "vendor phone number that provided the service"
                    },
                    "vendor email": {
                        "type": "string",
                        "description": "vendor email that provided the service"
                    },
                    "odometer": {
                        "type": "string",
                        "description": "odometer if exist"
                    },
                    "total_price": {
                        "type": "string",
                        "description": "Total price for the invoice"
                    },
                    "comments": {
                        "type": "string",
                        "description": "Any (technician) comments that are related to the purchases"
                    },
                    "parts/labors": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "item_name": {
                                    "type": "string",
                                    "description": "name or description of part/labor that is served"
                                },
                                "quantity": {
                                    "type": "string",
                                    "description": "Quantity for the item"
                                },
                                "unit_price": {
                                    "type": "string",
                                    "description": "Rate or unit price for the item"
                                },
                                "amount": {
                                    "type": "string",
                                    "description": "Extended Amount for the part/labor"
                                },
                                "order_type": {
                                    "type": "string",
                                    "description": "Select the charge from the following list: [part, labor]"
                                }
                            },
                            "required": ["item_name", "quantity", "unit_price", "amount", "order_type"]
                        }
                    }
                },
                "required": [
                    "invoice id/number",
                    "invoice date",
                    "vendor name",
                    "vendor address",
                    "vendor phone",
                    "total_price",
                    "parts/labors"
                ]
            }
        }
    }
]
