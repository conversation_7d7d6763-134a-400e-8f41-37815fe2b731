from datetime import datetime

INSTRUCTIONS = f"""
- You are an AI assistant that extracts load/shipment/order id, customer, weight, goods, total pay and the details of each pickup/delivery/stop from the context.
- Give only numbers in total pay attribute.
- List pickup/delivery/stop in the order you receive them in the context.
- appointment date MUST be in the format mm/dd/yyyy.
- appointment time MUST be in the format HH:MM.
- shipping/receiving hours MUST be in the format HH:MM - HH:MM.
- Current year is {datetime.today().year}, so put current year if input context does not have a year.
- Display only value in pickup/reference/order number.
- Do refrain including phone numbers.
- If one of the attributes is not presented, give empty string for that attribute.
- Total pay MUST be for the load/freight.
- The output has to be in JSON format.
"""

FUNCTIONS = [
    {
        "name": "extract_details",
        "type": "function",
        "description": "Extract load/shipment/order id, customer, weight, goods, total pay and the details of each pickup/delivery/stop in the order you receive them in the context",
        "parameters": {
            "type": "object",
            "properties": {
                "load/shipment id": {
                    "type": "string",
                    "description": "load/shipment/order id of Rate Confirmation",
                },
                "customer": {
                    "type": "string",
                    "description": "customer company name who ordered the load and usually customer company name comes on the top of the document. Remember: This is NOT a carrier company name.",
                },
                "weight": {
                    "type": "string",
                    "description": "weight of the load in lbs",
                },
                "goods": {
                    "type": "string",
                    "description": "goods or commodity - information about items that are being carried",
                },
                "total pay": {
                    "type": "string",
                    "description": "Total rate pay for the load or rate confirmation",
                },
                "stops": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "place name": {
                                "type": "string",
                                "description": "The name of the place.",
                            },
                            "apt/unit, street, city, state, zip code address": {
                                "type": "string",
                                "description": "apt/unit, street, city, state, zip code address of the place or pickup/delivery/stop.",
                            },
                            "appointment date": {
                                "type": "string",
                                "description": "The appointment date for the load that needs to be picked up or delivered.",
                            },
                            "appointment time": {
                                "type": "string",
                                "description": "The appointment time for the load that needs to be picked up or delivered in the format HH:MM.",
                            },
                            "shipping/receiving hours": {
                                "type": "string",
                                "description": "Shipping or receiving hours of the load.",
                            },
                            "pickup/reference/order number": {
                                "type": "string",
                                "description": "pickup/reference/order number",
                            },
                            "seal number": {
                                "type": "string",
                                "description": "Seal number",
                            },
                        },
                        "required": [
                            "apt/unit, street, city, state, zip code address",
                            "appointment date",
                            "appointment time",
                        ],
                    },
                },
            },
            "required": [
                "load/shipment id",
                "customer",
                "weight",
                "goods",
                "total pay",
                "stops",
            ],
        },
    }
]
