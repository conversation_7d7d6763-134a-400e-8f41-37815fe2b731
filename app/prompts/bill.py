INSTRUCTIONS = """
## INSTRUCTIONS:
- You are an AI assistant that extracts below given details from the receipt
- The output has to be in JSON format.
"""

TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "extract_details",
            "description": "Extract below given details from the receipt",
            "parameters": {
                "type": "object",
                "properties": {
                    "vendor name": {"type": "string", "description": "vendor name"},
                    "vendor address": {
                        "type": "string",
                        "description": "vendor address",
                    },
                    "bill date": {"type": "string", "description": "bill date"},
                    "transaction nature": {
                        "type": "string",
                        "description": "transaction nature",
                    },
                    "total pay": {"type": "string", "description": "total pay"},
                },
                "required": [
                    "vendor name",
                    "vendor address",
                    "bill date",
                    "transaction nature",
                    "total pay",
                ],
            },
        },
    }
]


FUNCTIONS = [
    {
        "name": "extract_vehicle_registration_details",
        "type": "function",
        "description": "Extract below given details from the receipt",
        "parameters": {
            "type": "object",
            "properties": {
                "vendor name": {"type": "string", "description": "vendor name"},
                "vendor address": {"type": "string", "description": "vendor address"},
                "bill date": {"type": "string", "description": "bill date"},
                "transaction nature": {
                    "type": "string",
                    "description": "transaction nature",
                },
                "total pay": {"type": "string", "description": "total pay"},
            },
            "required": [
                "vendor name",
                "vendor address",
                "bill date",
                "transaction nature",
                "total pay",
            ],
        },
    }
]

CATEGORY_INSTRUCTIONS = """
### INSTRUCTIONS:
- You are an assistant designed to figure out the category of a bill/invoice.
- Compare a bill/invoice to the provided descriptions for each category and select the most appropriate category.
- You must pay attention to any details in bill/invoice like purchased items, vendor name or any notes to find out the most appropriate category.

### CATEGORY DESCRIPTIONS
{{cat_descs}}

### OUTPUT
- You can only output in JSON format:
{
   "category": "",
   "explanation": ""
}
"""
