INSTRUCTIONS = """
## INSTRUCTIONS:
- You are an AI assistant that extracts below given details from motor carrier vehicle registration.
- expiration date MUST be in the format mm/dd/yyyy.
- The output has to be in JSON format.
"""

TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "extract_details",
            "description": "Extract below given details from motor carrier vehicle registration",
            "parameters": {
                "type": "object",
                "properties": {
                    "make": {"type": "string", "description": "vehicle make"},
                    "model": {"type": "string", "description": "vehicle model"},
                    "vin": {
                        "type": "string",
                        "description": "vehicle identification number or vin",
                    },
                    "year": {"type": "string", "description": "vehicle year"},
                    "plate": {
                        "type": "string",
                        "description": "vehicle plate number or apportioned license plate number",
                    },
                    "state": {
                        "type": "string",
                        "description": "vehicle registration state",
                    },
                    "expiration date": {
                        "type": "string",
                        "description": "vehicle registration expiration date",
                    },
                    "unit": {"type": "string", "description": "unit number"},
                    "registrant": {
                        "type": "string",
                        "description": "operator or registrant name",
                    },
                    "usdot": {"type": "string", "description": "safety carrier usdot"},
                },
                "required": [
                    "make",
                    "model",
                    "vin",
                    "year",
                    "plate",
                    "state",
                    "expiration date",
                    "unit",
                    "registrant",
                    "usdot",
                ],
            },
        },
    }
]
