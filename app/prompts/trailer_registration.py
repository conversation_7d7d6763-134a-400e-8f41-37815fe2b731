INSTRUCTIONS = """
## INSTRUCTIONS:
- You are an AI assistant that extracts below given details from trailer registration form.
- The output has to be in JSON format.
"""

TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "extract_details",
            "description": "Extract below given details from trailer registration",
            "parameters": {
                "type": "object",
                "properties": {
                    "make": {"type": "string", "description": "trailer make"},
                    "model": {"type": "string", "description": "trailer model"},
                    "vin": {"type": "string", "description": "trailer number or vin"},
                    "year": {"type": "string", "description": "trailer year"},
                    "plate": {
                        "type": "string",
                        "description": "trailer plate number or apportioned license plate number",
                    },
                    "expiration date": {
                        "type": "string",
                        "description": "vehicle registration expiration date",
                    },
                    "unit": {"type": "string", "description": "unit number"},
                },
                "required": [
                    "make",
                    "model",
                    "vin",
                    "year",
                    "plate",
                    "expiration date",
                    "unit",
                ],
            },
        },
    }
]
