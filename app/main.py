import time

import sentry_sdk
from fastapi import FastAPI
from sentry_sdk.integrations.celery import CeleryIntegration
from sentry_sdk.integrations.fastapi import FastApiIntegration
from starlette.middleware.cors import CORSMiddleware

from app.config import ENVIRONMENT, SENTRY_DSN, engine
from app.models.log import metadata
from app.logger import logger
from app.middleware import LogHandlerMiddleware
from app.routers import (
    bill,
    driver_license,
    rate_confirmation,
    safety,
    trailer_registration,
    truck_registration,
    work_order,
    pod_verification,
    bol_extraction
)

sentry_sdk.init(
    dsn=SENTRY_DSN,
    environment=ENVIRONMENT,
    send_default_pii=True,
    integrations=[
        FastApiIntegration(),
        CeleryIntegration(),
    ],
    enable_tracing=True,
    traces_sample_rate=0.5,
    profiles_sample_rate=0.5,
    trace_propagation_targets=["sentry-trace"]
)

app = FastAPI(title="TruckGPT API", version="0.2.0")
# app.add_middleware(LogHandlerMiddleware)

app.include_router(rate_confirmation.router)
app.include_router(bill.router)
app.include_router(driver_license.router)
app.include_router(truck_registration.router)
app.include_router(trailer_registration.router)
app.include_router(work_order.router)
app.include_router(safety.router)
app.include_router(pod_verification.router)
app.include_router(bol_extraction.router)


@app.get("/health")
async def health():
    return {"status": "ok"}


@app.on_event("startup")
def startup():
    print("Database connected and tables created.")


@app.on_event("shutdown")
def shutdown():
    engine.dispose()
    print("Database connection closed.")


@app.middleware("http")
async def add_process_time_header(request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    logger.info(
        f"Process time: {process_time:.2f} with status code: {response.status_code}"
    )
    return response


origins = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
