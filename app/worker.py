import sentry_sdk
from sentry_sdk.integrations.celery import CeleryIntegration

from celery import Celery

from app.config import *

sentry_sdk.init(
    dsn=SENTRY_DSN,
    integrations=[
        CeleryIntegration(),
    ],
    traces_sample_rate=1.0,
)

celery_app = Celery(
    'celery_app',
    broker=CELERY_BROKER_URI,
    broker_connection_retry_on_startup=True,
    backend=REDIS_URI,
    include=['app.tasks'],
    worker_concurrency=10,
    worker_max_tasks_per_child=20,
    worker_prefetch_multiplier=1,
)
