from datetime import datetime, time

from dateutil import parser

from dateutil.parser import ParserError
from fastapi import HTTPException


def clean_text(text: str):
    return text.replace('\x00', '').replace('\x02', '').replace('\x0c', '').replace('\x0b', '').replace('\x0e', '')


def clean_datetime(date_time):
    if date_time is None:
        return datetime.today().strftime('%Y-%m-%d %H:%M')
    else:
        date_time = date_time.replace('(', '').replace(')', '')
        return date_time


def set_current_year(date_time):
    if abs(date_time.year - datetime.today().year) > 1:
        date_time = date_time.replace(year=datetime.today().year)
    return date_time


def clean_response(text):
    try:
        return text['choices'][0]['message']['content'].strip()
    except KeyError or IndexError or TypeError:
        raise HTTPException(status_code=400, detail="OpenAI error")


def validate_load_id(value: str):
    value = value.strip().replace("#", "").replace("AXLL-", "")
    length = len(value)
    half_length = int(length / 2)

    if length <= 1:  # Base case: if the string is too short to split
        return value

    if length % 2 == 0 and value[:half_length] == value[half_length:]:
        return validate_load_id(value[half_length:])  # Recursive call on the second half
    else:
        return value


def parse_date(date: str):
    try:
        parsed_date = parser.parse(date)
    except (ParserError, TypeError):
        parsed_date = datetime.today()
    return parsed_date


def get_enter_exit_time(stop: dict):
    if stop.get('appointment date', '') == '':
        enter_date = exit_date = datetime.today().strftime('%Y-%m-%d')
    elif len(stop.get('appointment date').split('-')) == 2:
        enter_date = stop.get('appointment date').split('-')[0]
        exit_date = stop.get('appointment date').split('-')[1]
    else:
        enter_date = exit_date = stop.get('appointment date')

    if stop.get('appointment time', '') == '' and stop.get('shipping/receiving hours', '') == '':
        enter_time, exit_time = time(0, 1).strftime('%H:%M'), time(23, 59).strftime('%H:%M')
    elif stop.get('appointment time', '') != '' and stop.get('shipping/receiving hours', '') == '':
        if len(stop.get('appointment time', '').split('-')) == 2:
            enter_time = stop.get('appointment time').split('-')[0]
            exit_time = stop.get('appointment time').split('-')[1]
        else:
            enter_time = exit_time = stop.get('appointment time')
    elif stop.get('appointment time', '') == '' and stop.get('shipping/receiving hours', '') != '':
        if len(stop.get('shipping/receiving hours', '').split('-')) == 2:
            enter_time = stop.get('shipping/receiving hours').split('-')[0]
            exit_time = stop.get('shipping/receiving hours').split('-')[1]
        else:
            enter_time = exit_time = stop.get('shipping/receiving hours')
    else:
        if len(stop.get('appointment time', '').split('-')) == 2:
            enter_time = stop.get('appointment time').split('-')[0]
            exit_time = stop.get('appointment time').split('-')[1]
        elif len(stop.get('shipping/receiving hours', '').split('-')) == 2:
            enter_time = stop.get('shipping/receiving hours').split('-')[0]
            exit_time = stop.get('shipping/receiving hours').split('-')[1]
        else:
            enter_time = exit_time = stop.get('appointment time')

    # if stop.get('appointment time', '') == '':
    #     enter_time, exit_time = time(0, 1).strftime('%H:%M'), time(23, 59).strftime('%H:%M')
    # elif len(stop.get('appointment time').split('-')) == 2:
    #     enter_time = stop.get('appointment time').split('-')[0]
    #     exit_time = stop.get('appointment time').split('-')[1]
    # else:
    #     enter_time = exit_time = stop.get('appointment time')

    enter_date = clean_datetime(enter_date)
    exit_date = clean_datetime(exit_date)
    enter_time = clean_datetime(enter_time)
    exit_time = clean_datetime(exit_time)

    try:
        enter_datetime = parser.parse(enter_date + ' ' + enter_time)
    except ParserError:
        enter_datetime = datetime.combine(datetime.today(), time(0, 1))

    try:
        exit_datetime = parser.parse(exit_date + ' ' + exit_time)
    except ParserError:
        exit_datetime = datetime.combine(datetime.today(), time(23, 59))

    enter_datetime = set_current_year(enter_datetime).replace(tzinfo=None)
    exit_datetime = set_current_year(exit_datetime).replace(tzinfo=None)

    return enter_datetime, exit_datetime


def get_enter_exit_time_vision(stop: dict):
    if len(stop.get('earlies/latest dates', '').split('-')) == 2:
        enter_date = stop.get('earlies/latest dates').split('-')[0]
        exit_date = stop.get('earlies/latest dates').split('-')[1]
    elif len(stop.get('appointment date', '').split('-')) == 2:
        enter_date = stop.get('appointment date').split('-')[0]
        exit_date = stop.get('appointment date').split('-')[1]
    elif stop.get('earlies/latest dates', '') != '':
        enter_date = exit_date = stop.get('earlies/latest dates')
    elif stop.get('appointment date', '') != '':
        enter_date = exit_date = stop.get('appointment date')
    else:
        enter_date = exit_date = datetime.today().strftime('%Y-%m-%d')

    if len(stop.get('earlies/latest hours', '').split('-')) == 2:
        enter_time = stop.get('earlies/latest hours').split('-')[0]
        exit_time = stop.get('earlies/latest hours').split('-')[1]
    elif len(stop.get('appointment time', '').split('-')) == 2:
        enter_time = stop.get('appointment time').split('-')[0]
        exit_time = stop.get('appointment time').split('-')[1]
    elif stop.get('earlies/latest hours', '') != '':
        enter_time = exit_time = stop.get('earlies/latest hours')
    elif stop.get('appointment time', '') != '':
        enter_time = exit_time = stop.get('appointment time')
    else:
        enter_time = exit_time = datetime.today().strftime('%H:%M')

    enter_date = clean_datetime(enter_date)
    exit_date = clean_datetime(exit_date)
    enter_time = clean_datetime(enter_time)
    exit_time = clean_datetime(exit_time)

    try:
        enter_datetime = parser.parse(enter_date + ' ' + enter_time)
    except ParserError:
        try:
            enter_date = clean_datetime(stop.get('appointment date'))
            enter_time = clean_datetime(stop.get('appointment time'))
            enter_datetime = parser.parse(enter_date + ' ' + enter_time)
        except ParserError:
            enter_datetime = datetime.combine(datetime.today(), time(0, 1))

    try:
        exit_datetime = parser.parse(exit_date + ' ' + exit_time)
    except ParserError:
        try:
            exit_date = clean_datetime(stop.get('appointment date'))
            exit_time = clean_datetime(stop.get('appointment time'))
            exit_datetime = parser.parse(exit_date + ' ' + exit_time)
        except ParserError:
            exit_datetime = datetime.combine(datetime.today(), time(23, 59))

    enter_datetime = set_current_year(enter_datetime).replace(tzinfo=None)
    exit_datetime = set_current_year(exit_datetime).replace(tzinfo=None)

    return enter_datetime, exit_datetime
