from sqlalchemy import Table, Column, String, DateTime, Text, Integer, JSON
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from typing import Optional, TypedDict
from uuid import UUID
from datetime import datetime
import uuid
from app.config import metadata


log_transaction = Table(
    "log_transaction",
    metadata,
    Column("id", PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
    <PERSON>umn("request_id", PG_UUID(as_uuid=True), nullable=False),
    Column("scenario", String(255), nullable=False),  # Scenario with length
    Column("prompt_text", JSON, nullable=False),  # Prompt 
    Column("output", JSON, nullable=True),  # TruckGPT response
    Column("input_file", String(255), nullable=True),  # S3 file path with length
    Column("gpt_response", JSON, nullable=True),  # GPT response
    Column("transaction_at", DateTime, nullable=False),  # Time when transaction is recorded
    Column("error", Text, nullable=True),  # Error message
    Column("openai_response_time_ms", Integer, nullable=True),  # OpenAI model response time in ms
    Column("all_response_time_ms", Integer, nullable=True),  # Total process time in ms
    Column("error_code", String(50), nullable=True),  # Error code with length
)

class LogTransactionType(TypedDict):
    id: UUID
    request_id: UUID
    scenario: str
    prompt_text: dict
    output: Optional[dict]
    input_file: str
    gpt_response: Optional[dict]
    transaction_at: datetime
    error: Optional[str]
    openai_response_time_ms: Optional[int]
    all_response_time_ms: Optional[int]
    error_code: Optional[str]
