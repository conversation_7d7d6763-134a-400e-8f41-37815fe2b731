from datetime import datetime

from starlette.requests import Request
from starlette.concurrency import iterate_in_threadpool
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import Message


class CustomBaseHTTPMiddleware(BaseHTTPMiddleware):
    async def __call__(self, scope, receive, send):
        try:
            await super().__call__(scope, receive, send)
        except RuntimeError as exc:
            if str(exc) == 'No response returned.':
                request = Request(scope, receive=receive)
                if await request.is_disconnected():
                    return
            raise

    async def dispatch(self, request, call_next):
        raise NotImplementedError()


class LogHandlerMiddleware(CustomBaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app=app)

    async def set_body(self, request: Request):
        receive_ = await request._receive()

        async def receive() -> Message:
            return receive_

        request._receive = receive

    async def dispatch(self, request, call_next):
        await self.set_body(request)  # set body to request
        request_body = await request.body()
        request_time = datetime.now()
        response = await call_next(request)

        request_query_params = request.query_params
        db = request_query_params.get('db', 'unknown')

        response_body_data = [chunk async for chunk in response.body_iterator]
        response.body_iterator = iterate_in_threadpool(iter(response_body_data))

        try:
            response_body_text = response_body_data[0].decode()
        except (IndexError, UnicodeDecodeError):
            response_body_text = None

        taken_time = (datetime.now() - request_time).total_seconds()

        response.headers["X-Process-Time"] = f'{taken_time:0.4f} s'

        try:
            request_body = request_body.decode("utf-8")
        except UnicodeDecodeError:
            request_body = None

        log_entry = {
            "db": db,
            "response_status_code": response.status_code,
            "taken_time": taken_time,
            "request_path": request.url.path,
            "request_method": request.method,
            "request_time": request_time,
            "response_time": datetime.now(),
            "request_body": request_body,
            "response_body": response_body_text,
            "request_query_params": request_query_params,
            "request_headers": str(request.headers),
            "response_headers": str(response.headers)
        }

        return response
