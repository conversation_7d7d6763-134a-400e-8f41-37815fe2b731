import uuid
import json
from app.prompts.rate_confirmation import INSTRUCTIONS, FUNCTIONS
from app.prompts.rate_confirmation_vision import INSTRUCTIONS as INSTRUCTIONS_VISION, TOOLS
from app.utils.file import download_file, remove_file
from app.utils.prompt import get_vision_prompt
from app.worker import celery_app
from app.schemas.truckgpt_response import TruckGptServiceResponseType
from app.models.log import LogTransactionType
from app.scenarios import Scenario
from app.tasks.base import BaseTask
from app.utils.content import get_content
from app.services.gpt import truck_gpt, truck_gpt_vision, truck_gpt_tool
from app.parser.rate_confirmation import parse_rate_confirmation
from app.services.log import create_log_record


@celery_app.task(base=BaseTask, bind=True, name='task.rate_confirmation', queue='truckgpt')
def task_rate_confirmation(
        self,
        file_url: str,
        file_id: str,
        customer: str,
        mc_number: str,
        access_token: str,
        success_callback_url: str,
        failure_callback_url: str,
        is_premium: bool = False,
):
    request_id = uuid.uuid4()
    # download_file
    file_path = download_file(file_url)

    # get_content
    content = get_content(file_path=file_path, is_premium=is_premium)

    # send_to_openai
    response: TruckGptServiceResponseType = truck_gpt(
        instruction=INSTRUCTIONS,
        functions=FUNCTIONS,
        content=content
    )
    log_record: LogTransactionType = {
        "scenario": Scenario.RATE_CONFIRMATION.value,
        "input_file": file_url,
        "gpt_response": response["raw_response"],
        "request_id": request_id,
        "prompt_text": response["prompt"]
    }
    if not response['success']:
        log_record["error"] = response['error']
        create_log_record(**log_record)
        return response['error']
    result = parse_rate_confirmation(response['result'])
    log_record["output"] = result
    create_log_record(**log_record)
    remove_file(file_path)

    return result


@celery_app.task(base=BaseTask, bind=True, name='task.rate_confirmation_vision', queue='truckgpt')
def task_rate_confirmation_vision(
        self,
        file_url: str,
        file_id: str,
        customer: str,
        mc_number: str,
        access_token: str,
        success_callback_url: str,
        failure_callback_url: str,
):
    request_id = uuid.uuid4()
    # download_file
    file_path = download_file(file_url)

    # content = get_content(file_path=file_path, is_premium=True)
    #
    # response: TruckGptServiceResponseType = truck_gpt_tool(
    #     instruction=INSTRUCTIONS_VISION,
    #     content=content,
    #     tools=TOOLS
    # )
    #
    # remove_file(file_path)

    # detect_text_images
    prompt = get_vision_prompt(file_path=file_path, instructions=INSTRUCTIONS_VISION)

    # send_to_openai
    response: TruckGptServiceResponseType = truck_gpt_vision(
        prompt=prompt,
        tools=TOOLS
    )
    log_record: LogTransactionType = {
        "scenario": Scenario.RATE_CONFIRMATION.value,
        "input_file": file_url,
        "gpt_response": response["raw_response"],
        "request_id": request_id,
        "prompt_text": response["prompt"]
    }
    if not response['success']:
        log_record["error"] = response['error']
        create_log_record(**log_record)
        return response['error']
    # parse_response
    result = parse_rate_confirmation(response['result'])
    log_record["output"] = result
    create_log_record(**log_record)
    return result
