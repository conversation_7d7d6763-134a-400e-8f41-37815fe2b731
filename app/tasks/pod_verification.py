import uuid
from app.utils.file import download_file, remove_file
from app.utils.prompt import get_vision_prompt
from app.services.gpt import truck_gpt_vision
from app.services.log import create_log_record
from app.worker import celery_app
from app.scenarios import Scenario
from app.prompts.pod_verification import INSTRUCTIONS, TOOLS
from app.parser.pod_verification import parse_pod_verification
from app.models.log import LogTransactionType
from app.tasks.base import BaseTask
from app.schemas.truckgpt_response import TruckGptServiceResponseType


@celery_app.task(base=BaseTask, bind=True, name="task.pod_verification", queue="truckgpt")
def task_pod_verification(
    self,
    file_url: str,
):
    request_id = uuid.uuid4()

    file_path = download_file(file_url)
    prompt = get_vision_prompt(file_path, instructions=INSTRUCTIONS)

    response: TruckGptServiceResponseType = truck_gpt_vision(
        prompt=prompt, tools=TOOLS)

    log_record: LogTransactionType = {
        "scenario": Scenario.POD_VERIFICATION.value,
        "input_file": file_url,
        "gpt_response": response["raw_response"],
        "request_id": request_id,
        "prompt_text": response["prompt"]
    }

    if not response['success']:
        log_record["error"] = response["error"]
        create_log_record(**log_record)
        return response["error"]

    result = parse_pod_verification(response["result"])
    log_record["output"] = result
    create_log_record(**log_record)
    remove_file(file_path)

    return result
