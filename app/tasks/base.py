import json

import requests
import sentry_sdk
from celery import Task


class BaseTask(Task):
    abstract = True

    def on_success(self, retval, task_id, args, kwargs):
        print("Task Success")
        callback = kwargs.get('success_callback_url')
        if callback:
            self.send_success_callback(callback, retval, **kwargs)
        return super().on_success(retval, task_id, args, kwargs)

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        print("Task Failed")
        callback = kwargs.get('failure_callback_url')
        if callback:
            self.send_failure_callback(callback, exc, **kwargs)
        return super().on_failure(exc, task_id, args, kwargs, einfo)

    @staticmethod
    def send_success_callback(callback_url, data, **kwargs):
        access_token = kwargs.get('access_token')
        request_data = {
            "customer": kwargs.get('customer'),
            "mc_number": kwargs.get('mc_number'),
            "filename": kwargs.get('filename'),
            "user": kwargs.get('user'),
            "file": kwargs.get('file_id'),
            "load": data
        }
        json_data = json.dumps(request_data, indent=4, default=str)
        try:
            response = requests.post(
                callback_url,
                data=json_data,
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': access_token
                }
            )
            if response.status_code != 200:
                sentry_sdk.capture_message(f"Success Callback: {response.status_code}, {response.text}")
            print(f"Success Callback: {response.status_code}, {response.text}")
        except Exception as e:
            sentry_sdk.capture_message(f"Success Callback: {e}")
            print(f"Success Callback: {e}")

    @staticmethod
    def send_failure_callback(callback_url, data, **kwargs):
        access_token = kwargs.get('access_token')
        request_data = {
            "customer": kwargs.get('customer'),
            "mc_number": kwargs.get('mc_number'),
            "user": kwargs.get('user'),
            "filename": kwargs.get('filename'),
            "file": kwargs.get('file_id'),
            "error": data
        }
        json_data = json.dumps(request_data, indent=4, default=str)
        try:
            response = requests.post(
                callback_url,
                data=json_data,
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': access_token
                }
            )
            print(f"Failure Callback: {response.status_code}, {response.text}")
        except Exception as e:
            sentry_sdk.capture_message(f"Failure Callback: {e}")
            print(f"Failure Callback: {e}")
