import json
import datetime
import uuid

from app.config import session
from app.models.log import log_transaction
from app.utils import timeit


def json_serializable(data: dict) -> dict:
    """
    Convert any datetime objects in the dictionary to ISO format strings.
    """
    def default(obj):
        if isinstance(obj, (datetime.date, datetime.datetime)):
            return obj.isoformat()
        raise TypeError(f"Type {type(obj)} not serializable")
    
    return json.loads(json.dumps(data, default=default))


@timeit
def create_log_record(
    scenario: str,
    input_file: str,
    prompt_text: dict,
    gpt_response: dict,
    request_id: uuid.UUID,
    output: dict=None,
    error: str=None,
    openai_response_time_ms: int=None,
    all_response_time_ms: int=None,
    error_code: str = None,
):
    prompt_text = json_serializable(prompt_text)
    gpt_response = json_serializable(gpt_response)
    output = json_serializable(output) if output else None
    database = session()
    try:
        query = log_transaction.insert().values(
            id=uuid.uuid4(),
            request_id=request_id,
            scenario=scenario,
            prompt_text=prompt_text,
            output=output,
            input_file=input_file,
            gpt_response=gpt_response,
            transaction_at=datetime.datetime.now(),
            error=error,
            openai_response_time_ms=openai_response_time_ms,
            all_response_time_ms=all_response_time_ms,
            error_code=error_code,
        )
        database.execute(query)
        database.commit()
    except Exception as e:
        database.rollback()
        raise e
    finally:
        database.close()
