import hashlib
import json
import os
import uuid
from datetime import datetime
from urllib.parse import unquote
from typing import Optional, Union, List

import boto3
import requests
from botocore.exceptions import ClientError, NoCredentialsError
from fastapi import HTTPException

from app.config import AWS_DEFAULT_ACL, AWS_S3_REGION_NAME, AWS_STORAGE_BUCKET_NAME, ENVIRONMENT
from app.logger import logger


class AWS_S3:
    def __init__(self):
        self._s3 = None
        self.bucket_name = AWS_STORAGE_BUCKET_NAME
        self.region_name = AWS_S3_REGION_NAME
        self.default_acl = AWS_DEFAULT_ACL

    @property
    def s3(self):
        if self._s3 is None:
            try:
                self._s3 = boto3.client("s3")
            except NoCredentialsError:
                logger.error("AWS credentials not found")
                raise HTTPException(
                    status_code=500, detail="AWS credentials not configured")
        return self._s3

    def get_object_by_key(self, key: str) -> bytes:
        """Get S3 object by key and return bytes."""
        try:
            file_obj = self.s3.get_object(Bucket=self.bucket_name, Key=key)
            file_data: bytes = file_obj["Body"].read()
            return file_data
        except ClientError as e:
            logger.error(f"Failed to get S3 object {key}: {e}")
            raise HTTPException(
                status_code=404, detail=f"File not found: {key}")

    def get_file_by_url(self, file_url: str) -> bytes:
        """Get S3 file by URL."""
        file_url = unquote(file_url)
        key = file_url.replace(
            f"https://{self.bucket_name}.s3.amazonaws.com/", "")
        return self.get_object_by_key(key)

    def upload_file(self, file_path: str, s3_key: Optional[str] = None) -> str:
        """Upload a local file to S3 and return the S3 URL."""
        try:
            if s3_key is None:
                today = datetime.now()
                filename = os.path.basename(file_path)
                s3_key = f"uploads/{today.year}/{today.month}/{today.day}/{uuid.uuid4()}/{filename}"

            if ENVIRONMENT != "production":
                s3_key = f"staging/{s3_key}"

            self.s3.upload_file(
                file_path,
                self.bucket_name,
                s3_key,
                ExtraArgs={'ACL': self.default_acl}
            )

            s3_url = f"https://{self.bucket_name}.s3.amazonaws.com/{s3_key}"
            logger.info(f"Successfully uploaded {file_path} to S3: {s3_url}")
            return s3_url

        except FileNotFoundError:
            logger.error(f"File not found: {file_path}")
            raise HTTPException(
                status_code=404, detail=f"File not found: {file_path}")
        except ClientError as e:
            logger.error(f"S3 upload failed: {e}")
            raise HTTPException(
                status_code=500, detail=f"S3 upload failed: {str(e)}")

    def upload_content(self, content: bytes, filename: str, folder: str = "uploads") -> str:
        """Upload file content to S3 and return the S3 URL."""
        try:
            today = datetime.now()
            s3_key = f"{folder}/{today.year}/{today.month}/{today.day}/{uuid.uuid4()}/{filename}"

            if ENVIRONMENT != "production":
                s3_key = f"staging/{s3_key}"

            self.s3.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=content,
                ACL=self.default_acl
            )

            s3_url = f"https://{self.bucket_name}.s3.amazonaws.com/{s3_key}"
            logger.info(f"Successfully uploaded content to S3: {s3_url}")
            return s3_url

        except ClientError as e:
            logger.error(f"S3 upload failed: {e}")
            raise HTTPException(
                status_code=500, detail=f"S3 upload failed: {str(e)}")

    def copy_object_to_public(self, source_key: str) -> str:
        """Copy an S3 object to a public location."""
        try:
            today = datetime.now()
            filename = os.path.basename(source_key)
            filepath = f"{today.year}/{today.month}/{today.day}/{uuid.uuid4()}/{filename}"

            if ENVIRONMENT != "production":
                filepath = f"staging/{filepath}"

            destination = f"static/{filepath}"

            logger.info(
                f"Copy object source: {source_key}, destination: {destination}")

            self.s3.copy_object(
                CopySource={"Bucket": self.bucket_name, "Key": source_key},
                Bucket=self.bucket_name,
                Key=destination,
                ACL=self.default_acl,
            )

            return f"https://{self.bucket_name}.s3.amazonaws.com/{destination}"

        except ClientError as e:
            logger.error(f"S3 copy failed: {e}")
            raise HTTPException(
                status_code=500, detail=f"S3 copy failed: {str(e)}")

    def store_json(self, dirname: str, data) -> str:
        """Store JSON data in S3 and return the S3 path."""
        try:
            encoded_string = json.dumps(data).encode("utf-8")
            basepath = f"{ENVIRONMENT}/truckgpt-service/{dirname}"
            today = datetime.now()
            hashstr = hashlib.sha1(encoded_string).hexdigest()
            s3_path = f"{basepath}/{today.year}/{today.month}/{today.day}/{hashstr}.json"

            self.s3.put_object(
                Bucket=self.bucket_name,
                Key=s3_path,
                Body=encoded_string,
                ContentType="application/json",
                ACL=self.default_acl
            )

            logger.info(f"Stored JSON data to S3: {s3_path}")
            return s3_path

        except ClientError as e:
            logger.error(f"Failed to store JSON to S3: {e}")
            raise HTTPException(
                status_code=500, detail=f"Failed to store JSON: {str(e)}")

    def download_and_save_file(self, url: str, filename: str) -> str:
        """Download a file from URL and save to S3."""
        try:
            response = requests.get(url)
            response.raise_for_status()
            return self.upload_content(response.content, filename)
        except requests.RequestException as e:
            logger.error(f"Failed to download file from {url}: {e}")
            raise HTTPException(
                status_code=400, detail=f"Failed to download file: {str(e)}")

    def delete_object(self, key: str) -> bool:
        """Delete an object from S3."""
        try:
            self.s3.delete_object(Bucket=self.bucket_name, Key=key)
            logger.info(f"Successfully deleted S3 object: {key}")
            return True
        except ClientError as e:
            logger.error(f"S3 delete failed: {e}")
            return False

    @staticmethod
    def file_size(filedata: bytes) -> float:
        """Calculates the size of a file in megabytes.

        Args:
            filedata: The file data as bytes.

        Returns:
            The file size in megabytes (float).
        """
        file_size_in_bytes = len(filedata)
        file_size_in_mb = file_size_in_bytes / (1024 * 1024)
        return file_size_in_mb


# Global S3 service instance
aws_s3 = AWS_S3()


# Convenience functions
def upload_file_to_s3(file_path: str, s3_key: Optional[str] = None) -> str:
    """Upload a file to S3 and return the URL."""
    return aws_s3.upload_file(file_path, s3_key)


def upload_content_to_s3(content: bytes, filename: str, folder: str = "uploads") -> str:
    """Upload file content to S3 and return the URL."""
    return aws_s3.upload_content(content, filename, folder)


def delete_file_from_s3_url(serialized_urls: str):
    logger.info("[DELETE] Background task started...")
    aws_s3 = AWS_S3()

    try:
        urls = json.loads(serialized_urls)
        for url in urls:
            logger.info(f"[DELETE] Processing URL: {url}")
            bucket_url_prefix = f"https://{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com/"
            if not url.startswith(bucket_url_prefix):
                logger.warning(f"[DELETE] Invalid S3 URL: {url}")
                continue

            key = url[len(bucket_url_prefix):]
            aws_s3.delete_object(key)
            logger.info(f"[DELETE] Requested deletion for key: {key}")

    except Exception as e:
        logger.error(f"[DELETE] Failed to delete files: {e}")
