import json
import sentry_sdk
from openai import AzureOpenAI, OpenAIError

from app.schemas.truckgpt_response import TruckGptServiceResponseType
from app.config import *
from app.logger import logger
from app.utils import timeit

client = AzureOpenAI(
    api_key=OPENAI_API_KEY, api_version=OPENAI_API_VERSION, azure_endpoint=OPENAI_URL
)

client_vision = AzureOpenAI(
    api_key=OPENAI_VISION_API_KEY,
    api_version=OPENAI_VISION_API_VERSION,
    azure_endpoint=OPENAI_VISION_URL,
)

client_category = AzureOpenAI(
    api_key=OPENAI_CATEGORY_API_KEY,
    api_version=OPENAI_CATEGORY_API_VERSION,
    azure_endpoint=OPENAI_CATEGORY_URL,
)


@timeit  # temp
def truck_gpt_tool(
    instruction, tools, content, model=OPENAI_MODEL, max_tokens=2048, temperature=0
) -> TruckGptServiceResponseType:
    prompt = [
        {"role": "system", "content": instruction},
        {"role": "user", "content": f"""Context: {content}"""},
        {"role": "assistant", "content": "Output:"},
    ]
    logger.info("Sending request to OpenAI GPT-4 API")
    logger.info(f"OPENAI_API_VERSION: {OPENAI_API_VERSION}")
    logger.info(f"OPENAI_MODEL: {OPENAI_MODEL}")
    logger.info(f"OPENAI_URL: {OPENAI_URL}")
    logger.info(f"OPENAI_API: {OPENAI_API_KEY}")
    raw_response = ""
    try:
        response = client.chat.completions.create(
            model=model,
            messages=prompt,
            response_format={"type": "json_object"},
            temperature=temperature,
            max_tokens=max_tokens,
            tools=tools,
            tool_choice={"type": "function", "function": {"name": "extract_details"}},
        )
        prompt = prompt[0]
        raw_response = response.model_dump_json()
        try:
            output = response.choices[0].message.tool_calls[0].function.arguments
        except AttributeError as e:
            logger.error(e)
            output = response.choices[0].message.content
    except OpenAIError as e:
        prompt = prompt[0]
        logger.error(e)
        sentry_sdk.capture_exception(e)
        return TruckGptServiceResponseType(
            success=False, prompt=prompt, raw_response=raw_response, error=str(e)
        )
    try:
        json_output = json.loads(output)
    except json.JSONDecodeError as e:
        logger.error(output)
        sentry_sdk.capture_exception(e)
        return TruckGptServiceResponseType(
            success=False, prompt=prompt, raw_response=raw_response, error=str(e)
        )
    logger.info(f"GPT Response: {json_output}")
    return TruckGptServiceResponseType(
        success=True, prompt=prompt, raw_response=raw_response, result=json_output
    )


@timeit
def truck_gpt(
    instruction, functions, content, model=OPENAI_MODEL, max_tokens=2048, temperature=0
) -> TruckGptServiceResponseType:
    prompt = [
        {"role": "system", "content": instruction},
        {"role": "user", "content": f"""Context: {content}"""},
        {"role": "assistant", "content": "Output:"},
    ]
    logger.info("Sending request to OpenAI GPT-4 API")
    logger.info(f"OPENAI_API_VERSION: {OPENAI_API_VERSION}")
    logger.info(f"OPENAI_MODEL: {OPENAI_MODEL}")
    logger.info(f"OPENAI_URL: {OPENAI_URL}")
    logger.info(f"OPENAI_API: {OPENAI_API_KEY}")
    raw_response = ""
    try:
        response = client.chat.completions.create(
            model=model,
            messages=prompt,
            response_format={"type": "json_object"},
            temperature=temperature,
            max_tokens=max_tokens,
            functions=functions,
            function_call="auto",
        )
        prompt = prompt[0]
        raw_response = response.model_dump_json()
        try:
            output = response.choices[0].message.function_call.arguments
        except AttributeError as e:
            logger.error(e)
            output = response.choices[0].message.content
    except OpenAIError as e:
        prompt = prompt[0]
        logger.error(e)
        sentry_sdk.capture_exception(e)
        return TruckGptServiceResponseType(
            success=False, prompt=prompt, raw_response=raw_response, error=str(e)
        )
    try:
        json_output = json.loads(output)
    except json.JSONDecodeError as e:
        logger.error(output)
        sentry_sdk.capture_exception(e)
        return TruckGptServiceResponseType(
            success=False, prompt=prompt, raw_response=raw_response, error=str(e)
        )
    logger.info(f"GPT Response: {json_output}")
    return TruckGptServiceResponseType(
        success=True, prompt=prompt, raw_response=raw_response, result=json_output
    )


@timeit
def truck_gpt_vision(
    prompt, tools, model=OPENAI_VISION_MODEL, max_tokens=2048, temperature=0
) -> TruckGptServiceResponseType:
    logger.info("Sending request to OpenAI GPT-4 Vision API")
    logger.info(f"OPENAI_VISION_API_VERSION: {OPENAI_VISION_API_VERSION}")
    logger.info(f"OPENAI_VISION_MODEL: {OPENAI_VISION_MODEL}")
    logger.info(f"OPENAI_VISION_URL: {OPENAI_VISION_URL}")
    logger.info(f"OPENAI_VISION_API_KEY: {OPENAI_VISION_API_KEY}")
    complete_prompt = {"prompt": prompt, "tool": tools}
    raw_response = ""
    try:
        response = client_vision.chat.completions.create(
            model=model,
            messages=prompt,
            response_format={"type": "json_object"},
            temperature=temperature,
            max_tokens=max_tokens,
            tools=tools,
            tool_choice={"type": "function", "function": {"name": "extract_details"}},
        )
        raw_response = response.model_dump_json()
        output = response.choices[0].message.tool_calls[0].function.arguments
        complete_prompt["prompt"] = prompt[0]
    except (ValueError, OpenAIError) as e:
        complete_prompt["prompt"] = prompt[0]
        logger.error(e)
        sentry_sdk.capture_exception(e)
        return TruckGptServiceResponseType(
            success=False,
            prompt=complete_prompt,
            raw_response=raw_response,
            error=str(e),
        )

    try:
        json_output = json.loads(output)
    except json.JSONDecodeError as e:
        logger.error(output)
        sentry_sdk.capture_exception(e)
        return TruckGptServiceResponseType(
            success=False,
            prompt=complete_prompt,
            raw_response=raw_response,
            error=str(e),
        )

    logger.info(f"GPT Vision Response: {json_output}")
    return TruckGptServiceResponseType(
        success=True,
        prompt=complete_prompt,
        raw_response=raw_response,
        result=json_output,
    )


@timeit
def truck_gpt3(
    instruction,
    categories,
    content,
    model=OPENAI_CATEGORY_MODEL,
    max_tokens=1024,
    temperature=0,
) -> TruckGptServiceResponseType:
    logger.info("Sending request to OpenAI GPT-4 API (Categorization)")
    logger.info(f"OPENAI_API_VERSION: {OPENAI_CATEGORY_API_VERSION}")
    logger.info(f"OPENAI_MODEL: {OPENAI_CATEGORY_MODEL}")
    logger.info(f"OPENAI_URL: {OPENAI_CATEGORY_URL}")
    logger.info(f"OPENAI_API_KEY: {OPENAI_CATEGORY_API_KEY}")

    prompt = [
        {"role": "system", "content": instruction.replace("{{cat_descs}}", categories)},
        {"role": "user", "content": f"""Bill/Invoice: {content}"""},
        {"role": "assistant", "content": "Output:"},
    ]
    raw_response = ""
    try:
        response = client_category.chat.completions.create(
            model=model,
            messages=prompt,
            response_format={"type": "json_object"},
            max_tokens=max_tokens,
            temperature=temperature,
        )
        raw_response = response.model_dump_json()
        output = response.choices[0].message.content
        prompt = prompt[0]
    except OpenAIError as e:
        prompt = prompt[0]
        logger.error(e)
        sentry_sdk.capture_exception(e)
        return TruckGptServiceResponseType(
            success=False, prompt=prompt, raw_response=raw_response, error=str(e)
        )

    try:
        json_output = json.loads(output)
    except json.JSONDecodeError as e:
        logger.error(output)
        sentry_sdk.capture_exception(e)
        return TruckGptServiceResponseType(
            success=False, prompt=prompt, raw_response=raw_response, error=str(e)
        )

    logger.info(f"GPT Categorization Response: {json_output}")
    return TruckGptServiceResponseType(
        success=True, prompt=prompt, raw_response=raw_response, result=json_output
    )
