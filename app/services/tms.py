import requests

from fastapi import H<PERSON>PException


def upload_file_to_tms(file_path: str, domain: str, access_token: str):
    """
    Uploads a file to the TMS system.
    Parameters:
    - file_path: The path to the file to be uploaded.
    - domain: The domain of the TMS system.
    - access_token: Bearer token for authorization.
    Returns:
    - The JSON response from the TMS system upon successful upload.
    Raises:
    - HTTPException: If the upload fails or the server returns a non-201 status code.
    """
    url = f"https://{domain}.datatruck.io/api/v1/file/"
    headers = {'Authorization': f'Bearer {access_token}'}

    with open(file_path, 'rb') as file:
        files = {'file': file}
        response = requests.post(url, headers=headers, files=files)

    if response.status_code != 201:
        # Enhancing error handling by providing more context in the exception
        error_detail = response.json().get('detail', 'Failed to upload file to TMS.')
        raise HTTPException(status_code=response.status_code, detail=error_detail)

    return response.json()
