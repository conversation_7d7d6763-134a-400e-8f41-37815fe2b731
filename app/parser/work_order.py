from app.cleaner import parse_date
from app.utils import convert_to_float


def parse_work_order(response):
    parts = response.get('parts/labors', [])
    for part in parts:
        part['item_name'] = part.get('item_name', '')
        part['quantity'] = convert_to_float(part.get('quantity', '0'))
        part['amount'] = convert_to_float(part.get('amount', '0'))
        part['unit_price'] = convert_to_float(part.get('unit_price', '0'))
        part['order_type'] = part.get('order_type', 'part')
    result = {
        'invoice_id': response.get('invoice id/number', ''),
        'invoice_date': parse_date(response.get('invoice date')),
        'vendor_name': response.get('vendor name', ''),
        'vendor_address': response.get('vendor address', ''),
        'vendor_phone': response.get('vendor phone', ''),
        'vendor_email': response.get('vendor email', ''),
        'total_price': convert_to_float(response.get('total_price', '0')),
        'parts': parts,
    }
    return result
