from app.cleaner import parse_date
from app.utils import convert_to_float


def parse_bill(response):
    result = {
        'vendor_name': response.get('vendor name', ''),
        'vendor_address': response.get('vendor address', ''),
        'bill_date': parse_date(response.get('bill date')),
        'transaction_nature': response.get('transaction nature', ''),
        'total_pay': convert_to_float(response.get('total pay', '')),
    }
    return result


def parse_bill_category(response, response_category):
    result = {
        'vendor_name': response.get('vendor name', ''),
        'vendor_address': response.get('vendor address', ''),
        'bill_date': parse_date(response.get('bill date')),
        'transaction_nature': response.get('transaction nature', ''),
        'total_pay': convert_to_float(response.get('total pay', '')),
        'category': response_category.get('category', ''),
        'explanation': response_category.get('explanation', ''),
    }
    return result
