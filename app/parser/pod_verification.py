def parse_pod_verification(response):
    return {
        "pickup_address": response.get("pickup_address", ""),
        "destination_address": response.get("destination_address", ""),
        "max_page_count": response.get("max_page_count", -1),
        "current_page_count": response.get("current_page_count", 0),
        "auth_mark_type": response.get("auth_mark_type", 0),
        "po_number": response.get("po_number", "")
    }


def parse_pod_crosscheck_verification(response):
    return {
        "approve": response.get("approve", False),
        "reason": response.get("reason", ""),
        "max_page_count": response.get("max_page_count", -1),
        "current_page_count": response.get("current_page_count", 0),
        "auth_mark_type": response.get("auth_mark_type", 0),
    }
