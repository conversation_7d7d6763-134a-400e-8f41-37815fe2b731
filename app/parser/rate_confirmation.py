import re

from app.cleaner import validate_load_id, get_enter_exit_time, get_enter_exit_time_vision
from app.utils import convert_to_float


def parse_rate_confirmation(response: dict):
    stops = response.get('stops')
    length_of_stops = len(stops)

    # parse_response
    result = {
        'load_id': validate_load_id(response.get('load/shipment id')),
        'customer': None,  # response.get('customer')
        'weight': response.get('weight'),
        'goods': response.get('goods'),
        'load_pay': convert_to_float(response.get('total pay')),
        'stops': []
    }

    for i, stop in enumerate(stops):
        enter_time, exit_time = get_enter_exit_time(stop)

        address = stop.get('apt/unit, street, city, state, zip code address')
        zip_code = re.findall(
            r'\d{5}', address)[-1] if re.findall(r'\d{5}', address) else ''

        result['stops'].append({
            'company_name': stop.get('place name', ''),
            'address': address,
            'zip_code': zip_code,
            'enter_time': enter_time.strftime('%Y-%m-%d %H:%M'),
            'exit_time': exit_time.strftime('%Y-%m-%d %H:%M'),
            'reference_id': stop.get('pickup/reference/order number', ''),
            'seal_number': stop.get('seal number', ''),
            'stop_type': 'delivery' if i == length_of_stops - 1 else 'pickup'
        })

    return result


def parse_rate_confirmation_vision(response: dict):
    stops = response.get('stops')
    length_of_stops = len(stops)

    # parse_response
    result = {
        'load_id': validate_load_id(response.get('load/shipment id')),
        'customer': response.get('customer'),
        'customer_email': response.get('customer_email'),
        'weight': response.get('weight'),
        'goods': response.get('goods'),
        'load_pay': convert_to_float(response.get('total pay')),
        'hazmat': response.get('hazmat', False),
        'customer_agent': response.get('customer_agent', {}),
        'stops': [],
        'po_number': response.get('po_number', ''),
    }

    for i, stop in enumerate(stops):
        enter_time, exit_time = get_enter_exit_time_vision(stop)

        address = stop.get('apt/unit, street, city, state, zip code address')
        zip_code = re.findall(
            r'\d{5}', address)[-1] if re.findall(r'\d{5}', address) else ''

        result['stops'].append({
            'company_name': stop.get('place name', ''),
            'address': address,
            'zip_code': zip_code,
            'enter_time': enter_time.strftime('%Y-%m-%d %H:%M'),
            'exit_time': exit_time.strftime('%Y-%m-%d %H:%M'),
            'reference_id': stop.get('pickup/reference/order number', ''),
            'seal_number': stop.get('seal number', ''),
            'stop_type': 'delivery' if i == length_of_stops - 1 else 'pickup'
        })

    return result
