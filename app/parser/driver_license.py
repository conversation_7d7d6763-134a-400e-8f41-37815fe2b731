from app.cleaner import parse_date


def parse_driver_license(response):
    result = {
        'first_name': response.get('first_name', ''),
        'last_name': response.get('last_name', ''),
        'birthdate': parse_date(response.get('birthdate')).strftime('%Y-%m-%d'),
        'driver_address': response.get('address', ''),
        'driver_license_id': response.get('id', ''),
        'driver_license_state': response.get('state', ''),
        'driver_license_expiration_date': parse_date(response.get('expiration_date')).strftime('%Y-%m-%d'),
        'driver_license_issue_date': parse_date(response.get('issue_date')).strftime('%Y-%m-%d'),
    }
    return result
