from app.cleaner import parse_date


def parse_truck_registration(response):
    result = {
        'vehicle_make': response.get('make', ''),
        'vehicle_model': response.get('model', ''),
        'vehicle_identification_number': response.get('vin', ''),
        'vehicle_year': response.get('year', ''),
        'vehicle_plate_number': response.get('plate', ''),
        'vehicle_registration_state': response.get('state', ''),
        'vehicle_registration_expiration_date': parse_date(response.get('expiration date')).strftime("%Y-%m-%d"),
        'unit_number': response.get('unit', ''),
        'owner_registrant': response.get('registrant', ''),
        'usdot': response.get('usdot', ''),
    }
    return result
