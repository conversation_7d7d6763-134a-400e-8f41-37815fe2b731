from app.cleaner import parse_date
from app.schemas.safety import SafetyResponseSchema


def parse_safety(response) -> SafetyResponseSchema:
    violations = response.get("violations", [])
    for violation in violations:
        violation["violation_code"] = violation.get("violation_code", "")
        violation["violation_description"] = violation.get("violation_description", "")
    result = {
        "report_number": response.get("report_number", ""),
        "inspection_date": parse_date(response.get("inspection_date")).date(),
        "location_state": response.get("location_state", ""),
        "location_city": response.get("location_city", ""),
        "location": response.get("location", ""),
        "note": response.get("note", ""),
        "violations": violations,
    }
    return result
