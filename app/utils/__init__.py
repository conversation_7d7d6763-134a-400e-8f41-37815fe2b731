import re
import time

from app.logger import logger


def convert_to_float(number_string):
    # Remove dollar signs and commas
    if number_string is None:
        return 0.0
    cleaned_string = re.sub(r'[^\d.]', '', number_string)
    # Convert to float
    try:
        return float(cleaned_string)
    except ValueError:
        return 0.0


def timeit(method):
    def timed(*args, **kw):
        ts = time.time()
        result = method(*args, **kw)
        te = time.time()
        logger.info(f"{method.__name__} took: {te - ts} sec")
        return result

    return timed
