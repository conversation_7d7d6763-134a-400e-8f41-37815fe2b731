import os
from pathlib import Path
from typing import Union, List
from uuid import uuid4

import requests
from fastapi import UploadFile, HTTPException
from pdf2image import convert_from_bytes

from app.utils import timeit


def get_upload_file_from_path(file_path: str) -> UploadFile:
    # open file and return UploadFile
    file = open(file_path, 'rb')
    return UploadFile(file=file, filename=file_path.split('/')[-1])


def save_file(file: UploadFile):
    file_type = file.filename.split('.')[-1].lower()
    if not os.path.isdir('temp'):
        os.mkdir('temp')
    if file_type == 'pdf':
        file_path = os.path.join("temp/{}.{}".format(uuid4(), file_type))
        with open(file_path, 'wb') as my_file:
            my_file.write(file.file.read())
        return file_path
    elif file_type in ['jpg', 'jpeg', 'png']:
        file_path = os.path.join("temp/{}.{}".format(uuid4(), file_type))
        with open(file_path, 'wb') as my_file:
            my_file.write(file.file.read())
        return file_path
    else:
        raise HTTPException(status_code=400, detail="File type not supported")


@timeit
def download_file(url: str) -> str:
    file_type = url.split('.')[-1].lower()
    if len(file_type) > 5:
        file_name = Path('temp/{}.pdf'.format(uuid4()))
        with requests.get(url, stream=True) as r:
            r.raise_for_status()  # This will raise an error for a bad response
            with open(file_name, 'wb') as f:
                for chunk in r.iter_content(chunk_size=8192):
                    # If you have chunk encoded response uncomment if
                    # and set chunk_size parameter to None.
                    # if chunk:
                    f.write(chunk)
        return str(file_name)
    if not os.path.isdir('temp'):
        os.mkdir('temp')
    if file_type == 'pdf':
        file_path = os.path.join("temp/{}.{}".format(uuid4(), file_type))
        with open(file_path, 'wb') as my_file:
            my_file.write(requests.get(url).content)
        return file_path
    elif file_type in ['jpg', 'jpeg', 'png']:
        file_path = os.path.join("temp/{}.{}".format(uuid4(), file_type))
        with open(file_path, 'wb') as my_file:
            my_file.write(requests.get(url).content)
        return file_path
    else:
        raise HTTPException(status_code=400, detail="File type not supported")


def save_file_as_image(file_or_filepath: Union[UploadFile, str], is_file: bool = True) -> List[str]:
    if is_file:
        file = file_or_filepath
    else:
        file = get_upload_file_from_path(file_or_filepath)

    file_type = file.filename.split('.')[-1].lower()
    if file_type == 'pdf':
        file_paths = []
        images = convert_from_bytes(file.file.read())
        for i, image in enumerate(images):
            if i > 6:
                break  # Limiting the number of pages to 7
            file_path = os.path.join(f'temp/{uuid4()}.jpg')
            image.save(file_path, 'JPEG')
            file_paths.append(file_path)
        return file_paths
    elif file_type in ['jpg', 'jpeg', 'png']:
        file_path = os.path.join("temp/{}.{}".format(uuid4(), file_type))
        with open(file_path, 'wb') as my_file:
            my_file.write(file.file.read())
        return [file_path]
    else:
        raise HTTPException(status_code=400, detail="File type not supported")


def remove_file(file_path: str):
    try:
        os.remove(file_path)
        return True
    except FileNotFoundError:
        return False
