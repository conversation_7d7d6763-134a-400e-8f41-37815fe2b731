import io
import re
from fastapi import HTTPException
from google.cloud import vision
from pypdf import Pdf<PERSON>eader
from pypdf.errors import PdfStreamError
from typing import List

# Assuming save_file_as_image is well optimized and not shown here
from app.utils import timeit
from app.utils.file import save_file_as_image, remove_file


def detect_text_images(paths: list):
    client = vision.ImageAnnotatorClient()
    texts = []  # Collect texts in a list
    for path in paths:
        with io.open(path, 'rb') as image_file:
            content = image_file.read()
        image = vision.Image(content=content)
        response = client.document_text_detection(image=image)
        texts.append(response.full_text_annotation.text)
        remove_file(path)
    return "".join(texts)  # Efficient string joining


def parse_pdf(file_path):
    with open(file_path, 'rb') as file_object:
        try:
            pdf_reader = PdfReader(file_object)
        except PdfStreamError:
            raise HTTPException(status_code=400, detail="Invalid PDF file")
        pages_text = [page.extract_text() for page in pdf_reader.pages]
        return ' '.join(
            pages_text
        ).strip().replace('\x00', '').replace('\x02', '').replace('\x0c', '').replace('\x0b', '').replace('\x0e', '')


@timeit
def get_content(file_path, is_premium=False):
    contents = ''
    if not is_premium and file_path.endswith('.pdf'):
        contents = parse_pdf(file_path)

    length = len(contents)
    if length < 127 or is_premium:
        file_paths = save_file_as_image(file_path, is_file=False)
        contents = detect_text_images(paths=file_paths)
        length = len(contents)

    if length > 10000:
        contents = contents[:10000]

    return contents


@timeit
def get_content_from_files(file_paths: List[str], is_premium: bool = False) -> str:
    """
    Handles a list of file paths (PDFs and/or images) representing a single document.
    Mimics get_content(): first tries parsing all PDFs, falls back to OCR if needed.
    """
    content = ""

    # Step 1: Try to extract text from all PDFs (if not premium)
    if not is_premium:
        for path in file_paths:
            if path.lower().endswith(".pdf"):
                try:
                    parsed = parse_pdf(path)
                    if parsed:
                        content += f"\n\n{parsed}"
                except Exception as e:
                    print(
                        f"[get_content_from_files] Failed to parse PDF {path}: {e}")

    # Step 2: If PDF content is too short or is_premium, fallback to OCR on all files
    if len(content) < 127 or is_premium:
        all_image_paths = []
        for path in file_paths:
            try:
                page_images = save_file_as_image(path, is_file=False)
                all_image_paths.extend(page_images)
            except Exception as e:
                print(
                    f"[get_content_from_files] Failed to convert {path} to image: {e}")

        try:
            content = detect_text_images(paths=all_image_paths)
        except Exception as e:
            print(f"[get_content_from_files] OCR failed: {e}")
            content = ""

    # Step 3: Truncate if content exceeds 10,000 characters
    if len(content) > 10000:
        content = content[:10000]

    return content.strip()
