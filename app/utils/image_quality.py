import cv2
import numpy as np
import io
import os

from app.config import IMAGE_QUALITY_MIN_OCR_CONFIDENCE, IMAGE_QUALITY_MIN_PAGE_CONFIDENCE
from pathlib import Path
from app.utils.file import download_file
from pdf2image import convert_from_path
from PIL import Image
from google.cloud import vision
from fastapi import HTT<PERSON>Exception
from typing import List, Tuple
from app.services.s3 import upload_content_to_s3

client = vision.ImageAnnotatorClient()


def extract_images_and_upload(url: str, request_id: str, isPOD: bool = True) -> List[str]:
    file_type = get_file_type_from_url(url)
    file_path = download_file(url)
    image_urls = []

    # Handle PDF: convert all pages to images
    if file_type == "pdf":
        images = convert_from_path(
            file_path, dpi=400, fmt="jpeg", thread_count=4)

    # Handle image: load as a single image in list
    elif file_type == "image":
        try:
            image = Image.open(file_path)
            images = [image]
        except Exception as e:
            raise HTTPException(
                status_code=400, detail=f"Failed to open image: {e}")
    else:
        raise HTTPException(status_code=400, detail="Unsupported file type")

    # Perform quality check if it is POD
    if isPOD:
        quality_result, _ = process_document_quality(images)
        if not quality_result["approve"]:
            fail_reasons = "; ".join(
                f"Page {r['page']}: {r['fail_reason']}"
                for r in quality_result["results"]
                if r.get("fail_reason")
            )
            raise HTTPException(
                status_code=400,
                detail=f"Document failed quality check. Reasons: {fail_reasons or 'Unknown reason.'}"
            )

    # Upload images to S3
    for idx, image in enumerate(images):
        filename = f"page_{idx + 1}_{request_id}.jpg"
        buffer = io.BytesIO()
        image.save(buffer, format="JPEG")
        image_urls.append(upload_content_to_s3(buffer.getvalue(), filename))

    # Clean up
    if os.path.exists(file_path):
        os.remove(file_path)

    return image_urls


def get_file_type_from_url(url: str) -> str:
    suffix = Path(url).suffix.lower()
    if suffix == ".pdf":
        return "pdf"
    elif suffix in [".jpg", ".jpeg", ".png"]:
        return "image"
    else:
        raise HTTPException(
            status_code=400, detail="Unsupported file type: {}".format(suffix))


def load_images_from_file(file_path: str, grayscale: bool = False) -> List[Image.Image]:
    file_suffix = Path(file_path).suffix.lower()
    if file_suffix == ".pdf":
        return convert_from_path(file_path, dpi=400, fmt="jpeg", thread_count=4, grayscale=grayscale)
    elif file_suffix in [".jpg", ".jpeg", ".png"]:
        return [Image.open(file_path)]
    else:
        raise HTTPException(status_code=400, detail="Unsupported file type")


def load_all_images(file_paths: List[str]) -> List[Image.Image]:
    all_images = []
    for file_path in file_paths:
        images = load_images_from_file(file_path)
        all_images.extend(images)
    return all_images


def evaluate_document_quality(ocr_response) -> dict:
    try:
        confidences = [
            word.confidence
            for page in ocr_response.full_text_annotation.pages
            for block in page.blocks
            for para in block.paragraphs
            for word in para.words
            if hasattr(word, "confidence")
        ]
        ocr_conf = float(sum(confidences) / len(confidences)
                         * 100) if confidences else 0.0
    except:
        ocr_conf = 0.0

    return {
        "ocr_confidence": round(ocr_conf, 2)
    }


def get_max_allowed_bad_pages(total_pages: int) -> int:
    if total_pages <= 2:
        return 0
    return max(1, total_pages // 5)  # ~20% of pages can be low quality


def process_document_quality(images: List[Image.Image]) -> Tuple[dict, List[str]]:
    results = []
    ocr_texts = []
    for idx, img in enumerate(images):
        np_img = np.array(img.convert("RGB"))
        _, buffer = cv2.imencode(".png", np_img)
        vision_img = vision.Image(content=buffer.tobytes())
        ocr_response = client.document_text_detection(image=vision_img)

        page_result = evaluate_document_quality(ocr_response)
        page_result["page"] = idx + 1
        results.append(page_result)
        ocr_texts.append(ocr_response.full_text_annotation.text.strip())

    confidences = [r["ocr_confidence"] for r in results]
    avg_conf = sum(confidences) / len(confidences)
    low_conf_pages = sum(1 for conf in confidences if conf <
                         IMAGE_QUALITY_MIN_OCR_CONFIDENCE)
    unreadable = any(
        conf < IMAGE_QUALITY_MIN_PAGE_CONFIDENCE for conf in confidences)

    max_bad = get_max_allowed_bad_pages(len(images))
    approve = (
        avg_conf >= IMAGE_QUALITY_MIN_OCR_CONFIDENCE and
        low_conf_pages <= max_bad and
        not unreadable
    )

    fail_reason = None if approve else (
        "Some pages are hard to read" if unreadable else
        f"Too many unclear pages (maximum allowed: {max_bad})" if low_conf_pages > max_bad else
        f"The text is not clear enough to process (average clarity: {round(avg_conf, 2)})"
    )

    return {
        "approve": approve,
        "average_confidence": round(avg_conf, 2),
        "fail_reason": fail_reason,
        "results": results
    }, ocr_texts
