import io
import base64
from PIL import Image
from pathlib import Path
from google.cloud import vision
from pdf2image import convert_from_path
from fastapi import HTTPException
import cv2
import numpy as np
from app.utils.file import remove_file


def get_vision_prompt(file_path: str, instructions: str, is_remove_file: bool = True):
    file_suffix = Path(file_path).suffix.lower()
    if file_suffix == ".pdf":
        # Convert PDF to images directly in memory
        images = convert_from_path(
            file_path, dpi=400, fmt="jpeg", thread_count=4, grayscale=True
        )
    elif file_suffix in [".jpg", ".jpeg", ".png"]:
        # Load the image directly if it's an image file
        images = [Image.open(file_path)]
    else:
        # Return an error message for unsupported file types
        raise HTTPException(status_code=400, detail="Unsupported file type")

    prompt = [
        {"role": "system", "content": instructions}
    ]  # Assuming 'instruction' is defined elsewhere

    # Process each page in the PDF
    for i, image in enumerate(images):
        if i > 7:
            break  # Limit to 7 pages
        # Convert PIL image to a bytes buffer
        buffer = io.BytesIO()
        if image.mode != "RGB":
            image = image.convert("RGB")
        image.save(buffer, format="JPEG")
        # Encode image to base64 string
        encoded_string = base64.b64encode(buffer.getvalue()).decode("utf-8")

        # Append the encoded image to the prompt
        prompt.append(
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{encoded_string}"
                        },
                    }
                ],
            }
        )
    prompt.append({"role": "assistant", "content": "Output:"})

    if is_remove_file:
        remove_file(file_path)

    return prompt


def get_vision_and_text_prompt(file_path: str, instructions: str, alpha=1.5, beta=0):
    pdf_content = ""
    file_suffix = Path(file_path).suffix.lower()
    google_client = vision.ImageAnnotatorClient()
    prompt = [{"role": "system", "content": instructions}]
    if file_suffix == ".pdf":
        # Convert PDF to images directly in memory
        images = convert_from_path(pdf_path=file_path, dpi=400)
    elif file_suffix in [".jpg", ".jpeg", ".png"]:
        # Load the image directly if it's an image file
        images = [Image.open(file_path)]
    else:
        # Return an error message for unsupported file types
        raise HTTPException(status_code=400, detail="Unsupported file type")

    for i, image in enumerate(images):
        # Enhance contrast of the upscaled image
        enhanced_image = enhance_contrast(image, alpha, beta)

        # Convert enhanced image to JPEG buffer
        _, buffer = cv2.imencode(".png", enhanced_image)
        image_bytes = io.BytesIO(buffer)

        # Read the bytes buffer and convert it to content
        content = image_bytes.read()
        encoded_string = base64.b64encode(content).decode("utf-8")
        prompt.append(
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/png;base64,{encoded_string}"},
                    }
                ],
            }
        )

        # Google Vision read
        image_vision = vision.Image(content=content)
        response = google_client.document_text_detection(image=image_vision)
        image_content = response.full_text_annotation.text
        pdf_content = pdf_content + "\n" + image_content

    prompt.append(
        {
            "role": "user",
            "content": f"""### PDF Content # {pdf_content}""",
        }
    )
    prompt.append({"role": "assistant", "content": "Output:"})
    return prompt


def get_vision_text_prompt_with_ocr(
    images: list[Image.Image],
    instructions: str,
    per_page_ocr: list[str],
    alpha=1.5,
    beta=0
):
    prompt = [{"role": "system", "content": instructions}]

    if len(per_page_ocr) != len(images):
        raise ValueError("Mismatch between number of OCR texts and images")

    for i, image in enumerate(images):
        enhanced_image = enhance_contrast(image, alpha, beta)
        _, buffer = cv2.imencode(".png", enhanced_image)
        content = io.BytesIO(buffer).read()
        encoded_string = base64.b64encode(content).decode("utf-8")

        prompt.append({
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/png;base64,{encoded_string}"}
                }
            ]
        })

    pdf_content = "\n".join(per_page_ocr)
    prompt.append(
        {"role": "user", "content": f"""### File Content # {pdf_content}"""})

    prompt.append({"role": "assistant", "content": "Output:"})
    return prompt


def get_vision_text_prompts(
    images: list[Image.Image],
    instructions: str,
    alpha=1.5,
    beta=0
):
    pdf_content = ""
    google_client = vision.ImageAnnotatorClient()
    prompt = [{"role": "system", "content": instructions}]

    for i, image in enumerate(images):
        enhanced_image = enhance_contrast(image, alpha, beta)
        _, buffer = cv2.imencode(".png", enhanced_image)
        content = io.BytesIO(buffer).read()
        encoded_string = base64.b64encode(content).decode("utf-8")

        prompt.append({
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/png;base64,{encoded_string}"}
                }
            ]
        })

        # Google Vision read
        image_vision = vision.Image(content=content)
        response = google_client.document_text_detection(image=image_vision)
        image_content = response.full_text_annotation.text
        pdf_content = pdf_content + "\n" + image_content

    prompt.append(
        {
            "role": "user",
            "content": f"""### File Content # {pdf_content}""",
        }
    )

    prompt.append({"role": "assistant", "content": "Output:"})
    return prompt


def get_crosscheck_prompt(pod_urls: list[str], original_urls: list[str], instructions: str) -> list[dict]:
    prompt = [{"role": "system", "content": instructions}]

    # Add POD document images
    pod_images = [{"type": "image_url", "image_url": {"url": url}}
                  for url in pod_urls]
    prompt.append({
        "role": "user",
        "content": [
            {"type": "text",
                "text": "This is the POD (Proof of Delivery) document:"},
            *pod_images
        ]
    })

    # Add original document images
    original_images = [{"type": "image_url", "image_url": {
        "url": url}} for url in original_urls]
    prompt.append({
        "role": "user",
        "content": [
            {"type": "text", "text": "This is the original document:"},
            *original_images
        ]
    })

    # Final instruction for GPT
    prompt.append({"role": "assistant", "content": "Output:"})

    return prompt


def enhance_contrast(image, alpha=1, beta=0):
    # enhances the contrast of an input PIL image.
    image_np = np.array(image.convert("L"))  # grayscale conversion
    enhanced_np = cv2.convertScaleAbs(image_np, alpha=alpha, beta=beta)
    enhanced_image = cv2.cvtColor(enhanced_np, cv2.COLOR_GRAY2RGB)
    return enhanced_image
